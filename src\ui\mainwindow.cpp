#include "mainwindow.h"
#include <legacy/workers/filterworker.h>
#include <config/app_config.h>
#include <core/utils/logger.h>
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QGridLayout>
#include <QGroupBox>
#include <QLabel>
#include <QHeaderView>
#include <QFile>
#include <QTextStream>
#include <QMessageBox>
#include <QJsonObject>
#include <QJsonArray>
#include <QJsonValue>
#include <QDateTime>
#include <QDebug>
#include <QTimer>
#include <QMimeData>
#include <QDragEnterEvent>
#include <QDropEvent>
#include <QSpinBox>
#include <QMenu>
#include <QAction>
#include <QCheckBox>
#include <QRegularExpression>
#include <QStyledItemDelegate>
#include <QLineEdit>
#include <QRegularExpressionValidator>
#include <QSettings>
#include <QPlainTextEdit>
#include <QtConcurrent>
#include <QTextCursor>
#include <QTextBlock>
#include <QElapsedTimer>
#include <QFutureWatcher>
#include <QScrollBar>
#include <QInputDialog>

// ---------- 辅助函数 ----------
// 创建居中对齐的表格项
static QTableWidgetItem* createCenteredTableItem(const QString& text = "", const QColor& bgColor = QColor(255, 255, 255)) {
    auto item = new QTableWidgetItem(text);
    item->setTextAlignment(Qt::AlignCenter);
    item->setBackground(bgColor);
    return item;
}

// 添加表格行的辅助函数
static void addTableRow(QTableWidget* table, int row, const QStringList& texts) {
    for (int col = 0; col < texts.size() && col < table->columnCount(); ++col) {
        auto item = createCenteredTableItem(texts[col]);
        item->setToolTip(texts[col]);
        table->setItem(row, col, item);
    }
}

// ---------- 后台解析公共函数 ----------
static RefreshParseResult parseOrders(const QJsonArray &orders,
                                      bool firstBatch,
                                      const QString &key,
                                      QSet<QString> seenCopy,
                                      QHash<QString,double> priceCopy)
{
    RefreshParseResult r;  r.key = key;
    QSet<QString> &seen = seenCopy;
    QHash<QString,double> &last = priceCopy;

    for (const QJsonValue &v : orders) {
        QJsonObject o   = v.toObject();
        QString id      = o.value("SerialNo").toString();
        double  price   = o.value("Price").toDouble();

        bool seenBefore = seen.contains(id);
        bool treatNew   = !seenBefore || price >= last.value(id, price) + 5.0;
        last[id] = price;

        if (firstBatch) {
            // 首批刷新：仅作为基线，不计入 newOrders，但仍应记录已见 ID
            if (!seenBefore) {
                seen.insert(id);
                r.newSeenIds.insert(id);  // 让主线程更新 m_seenOrderIds
            }
            r.autoAcceptObjs.append(o);
            continue;
        }
        if (!treatNew) continue;

        if (!seenBefore) r.newSeenIds.insert(id);

        OrderInfo info;
        info.orderId     = id;
        info.title       = o.value("Title").toString();
        info.price       = QString::number(price);
        info.timeLimit   = QString::number(o.value("TimeLimit").toInt());
        info.createUser  = o.value("Create").toString();
        info.zoneServer  = o.value("Zone").toString();
        auto uidv        = o.value("UserID");
        info.publishUserId = uidv.isString() ? uidv.toString()
                          : QString::number(static_cast<qint64>(uidv.toDouble()));
        info.timestamp   = QDateTime::currentDateTime().toString("hh:mm:ss.zzz");

        r.newOrders.append(info);
        r.autoAcceptObjs.append(o);
    }
    r.newCount    = r.newOrders.size();
    r.newPriceMap = last;
    return r;
}
// ---------- 解析函数结束 ----------
// 数字输入委托：仅允许整数或小数
class NumericDelegate : public QStyledItemDelegate {
public:
    explicit NumericDelegate(QObject *parent = nullptr) : QStyledItemDelegate(parent) {}
    QWidget *createEditor(QWidget *parent, const QStyleOptionViewItem &, const QModelIndex &) const override {
        auto editor = new QLineEdit(parent);
        QRegularExpression re("^\\d*$");
        editor->setValidator(new QRegularExpressionValidator(re, editor));
        return editor;
    }
};

MainWindow::MainWindow(QWidget *parent)
    : QMainWindow(parent)
    , m_mainAcctWaiting(false)
    , m_mainAcctTimerWasActive(false)
    , m_isRefreshing(false)
    , m_currentAccountIndex(0)
    , m_batchLoginIndex(0)
    , m_isBatchLogin(false)
    , m_isMainAccountRefreshing(false)
    , m_waitingRefresh(false)
    , m_loadingSettings(false)
    , m_extraExclusionResult(0)
    , m_extraExclusionFinished(0)
{
    // 初始化配置系统 (第一阶段迁移)
    CONFIG.load();

    // 初始化新日志系统 (第二阶段迁移)
    Logger::instance().setLogLevel(NETWORK_CONFIG.enableDebugLog ? LogLevel::DEBUG : LogLevel::INFO);
    Logger::instance().setLogToConsole(true);

    // 初始化异步登录管理器
    m_asyncLoginManager = new AsyncLoginManager(this);

    // 应用程序启动初始化

    ui = new Ui::MainWindow();
    ui->setupUi(this);

    // 将Designer控件映射到旧成员指针，保持后续逻辑代码不变
    m_usernameEdit = ui->usernameEdit;
    m_passwordEdit = ui->passwordEdit;

    // 确保密码输入框显示明文
    if (m_passwordEdit) {
        m_passwordEdit->setEchoMode(QLineEdit::Normal);
        m_passwordEdit->setClearButtonEnabled(true);

        // 禁用点击时自动选中所有文本的行为
        connect(m_passwordEdit, &QLineEdit::selectionChanged, this, [this]() {
            if (m_passwordEdit->hasSelectedText()) {
                m_passwordEdit->deselect();
            }
        });
    }

    m_loginButton  = ui->loginButton;

    // 设置按钮的焦点策略，避免点击后自动跳转焦点
    if (m_loginButton) {
        m_loginButton->setFocusPolicy(Qt::NoFocus);
    }
    m_batchLoginButton = findChild<QPushButton*>("batchLoginButton");
    if (m_batchLoginButton) {
        m_batchLoginButton->setFocusPolicy(Qt::NoFocus);
    }
    m_refreshButton = ui->refreshButton;
    if (m_refreshButton) {
        m_refreshButton->setFocusPolicy(Qt::NoFocus);
    }
    m_stopButton   = ui->stopButton;
    if (m_stopButton) {
        m_stopButton->setFocusPolicy(Qt::NoFocus);
    }
    m_loadAccountsButton = findChild<QPushButton*>("loadAccountsButton");
    m_clearLogButton = findChild<QPushButton*>("clearLogButton");
    m_mainAccountRefreshButton = findChild<QPushButton*>("mainAccountRefreshButton");

    m_pageSizeSpin = findChild<QSpinBox*>("pageSizeSpin");
    m_gameCombo    = ui->gameCombo;

    m_priceFilterCheck = findChild<QCheckBox*>("priceFilterCheck");
    m_priceMinEdit = findChild<QLineEdit*>("priceMinEdit");
    m_priceMaxEdit = findChild<QLineEdit*>("priceMaxEdit");
    m_payPasswordEdit = findChild<QLineEdit*>("payPasswordEdit");
    if (m_payPasswordEdit) {
        // 设置为明文显示
        m_payPasswordEdit->setEchoMode(QLineEdit::Normal);
        m_payPasswordEdit->setClearButtonEnabled(true);
        // 仅允许输入数字
        m_payPasswordEdit->setValidator(new QRegularExpressionValidator(QRegularExpression("\\d+"), m_payPasswordEdit));

        // 禁用点击时自动选中所有文本的行为
        connect(m_payPasswordEdit, &QLineEdit::selectionChanged, this, [this]() {
            if (m_payPasswordEdit->hasSelectedText()) {
                m_payPasswordEdit->deselect();
            }
        });
    }
    m_focusOnlyCheck = findChild<QCheckBox*>("focusOnlyCheck");
    m_intervalCheck = findChild<QCheckBox*>("intervalCheck");
    m_intervalSpin  = findChild<QSpinBox*>("intervalSpin");
    m_mainAcctIntervalSpin = findChild<QSpinBox*>("mainAcctIntervalSpin");
    m_fingerprintCombo = findChild<QComboBox*>("fingerprintCombo");


    m_filterKeywordEdit = findChild<QLineEdit*>("filterKeywordEdit");
    m_excludeKeywordEdit = findChild<QLineEdit*>("excludeKeywordEdit");
    m_advancedModeCheck = findChild<QCheckBox*>("advancedModeCheck");
    m_advancedTable = findChild<QTableWidget*>("advancedTable");
    
    // 使用UI文件中定义的分组统计表格
    m_groupStatsTable = findChild<QTableWidget*>("groupStatsTable");
    if (m_groupStatsTable) {
        // 设置表格属性
        m_groupStatsTable->horizontalHeader()->setSectionResizeMode(0, QHeaderView::Fixed); // 固定列宽
        m_groupStatsTable->horizontalHeader()->setSectionResizeMode(1, QHeaderView::Fixed);
        m_groupStatsTable->setColumnWidth(0, 80); // 分组名称列宽度适合4个汉字
        m_groupStatsTable->setColumnWidth(1, 100); // 关键词数量列宽度适合5个汉字
        
        // 设置表头居中
        QHeaderView* hHeader = m_groupStatsTable->horizontalHeader();
        hHeader->setDefaultAlignment(Qt::AlignCenter);
    } else {
        // 如果UI中没有找到表格，则创建一个（作为备用方案）
        m_groupStatsTable = new QTableWidget(this);  // Qt父子管理，无需手动删除
        m_groupStatsTable->setObjectName("groupStatsTable");
        m_groupStatsTable->setColumnCount(2);
        QStringList headers;
        headers << "分组名称" << "关键词数量";
        m_groupStatsTable->setHorizontalHeaderLabels(headers);
        m_groupStatsTable->setEditTriggers(QAbstractItemView::NoEditTriggers);
        m_groupStatsTable->setAlternatingRowColors(true);
        m_groupStatsTable->horizontalHeader()->setSectionResizeMode(0, QHeaderView::Stretch);
        m_groupStatsTable->horizontalHeader()->setStretchLastSection(false);
        m_groupStatsTable->setColumnWidth(1, 80);
        
        // 放在窗口右下角作为浮动表格
        m_groupStatsTable->setGeometry(this->width() - 300, this->height() - 250, 280, 200);
    }

    m_accountTable = ui->accountTable;
    m_orderTable   = ui->orderTable;
    m_resultTable = findChild<QTableWidget*>("resultTable");

    // 使用 QPlainTextEdit 取代 QTextEdit 作为日志窗口
    QWidget *oldLog = findChild<QWidget*>("logText");
    QRect logRect = oldLog ? oldLog->geometry() : QRect(0,0,300,100);
    if (oldLog) oldLog->setVisible(false);
    m_logText = new QPlainTextEdit(this);  // Qt父子管理，无需手动删除
    m_logText->setGeometry(logRect);
    m_logText->setReadOnly(true);
    m_logText->setContextMenuPolicy(Qt::CustomContextMenu);
    // 新增：减小内存与排版开销
    m_logText->setUndoRedoEnabled(false);

    // 配置系统已初始化

    // 设置Tab顺序，跳过按钮避免焦点问题
    setTabOrder(m_usernameEdit, m_passwordEdit);
    setTabOrder(m_passwordEdit, m_payPasswordEdit);

    // 界面初始化完成
    m_logText->document()->setMaximumBlockCount(1000);

    setAcceptDrops(true); // 支持拖拽

    // 创建网络引擎切换界面
    createNetworkEngineControls();

    // 创建API线程
    // m_apiThread = new QThread(this);
    m_api = new OrderAPI(this); // Qt父子管理，无需手动删除
    // m_api->moveToThread(m_apiThread);
    
    // 连接信号槽
    connect(m_api, &OrderAPI::loginResult, this, &MainWindow::onLoginResult);
    connect(m_api, &OrderAPI::orderRefreshResult, this, &MainWindow::onOrderRefreshResult);
    connect(m_api, &OrderAPI::networkError, this, &MainWindow::onNetworkError);
    connect(m_api, &OrderAPI::userInfoResult, this, &MainWindow::onUserInfoResult);
    connect(m_api, &OrderAPI::focusUserResult, this, [this](bool ok, const QString &msg, const QString &fid){
        addLogMessage(QString("主账号 关注结果[%1]: %2").arg(fid).arg(ok ? (msg.isEmpty()?"成功":msg) : ("失败:"+msg)));
    });
    connect(m_api, &OrderAPI::cancelFocusUserResult, this, [this](bool ok, const QString &msg, const QString &fid){
        addLogMessage(QString("主账号 取消关注结果[%1]: %2").arg(fid).arg(ok ? (msg.isEmpty()?"成功":msg) : ("失败:"+msg)));
    });
    connect(m_api, &OrderAPI::addBlackResult, this, [this](bool ok, const QString &msg, const QString &uid){
        addLogMessage(QString("主账号 拉黑结果[%1]: %2").arg(uid).arg(ok?(msg.isEmpty()?"成功":msg):(QString("失败:")+msg)));
    });
    connect(m_api, &OrderAPI::removeBlackResult, this, [this](bool ok, const QString &msg, const QString &uid){
        addLogMessage(QString("主账号 取消拉黑结果[%1]: %2").arg(uid).arg(ok?(msg.isEmpty()?"成功":msg):(QString("失败:")+msg)));
    });
    connect(m_api, &OrderAPI::orderAcceptResult, this, &MainWindow::onOrderAcceptResult);
    
    // ===== 新增：快速抢单通道 =====
    connect(m_api, &OrderAPI::fastOrderFound, this, [this](const QJsonObject &orderObj){
        // 在主线程快速过滤并加入接单队列，避免额外 UI 往返
        if (orderMatchesFilter(orderObj)) {
            attemptAcceptOrder(orderObj, false);
        }
    }, Qt::QueuedConnection);
    
    // 启动API线程
    // m_apiThread->start();
    
    // 创建刷新定时器
    m_refreshTimer = new QTimer(this);  // Qt父子管理，无需手动删除
    connect(m_refreshTimer, &QTimer::timeout, this, &MainWindow::onRefreshTimer);

    // 主账号定时刷新
    m_mainAcctTimer = new QTimer(this);  // Qt父子管理，无需手动删除
    m_mainAcctTimer->setSingleShot(false);
    connect(m_mainAcctTimer, &QTimer::timeout, this, [this](){
        if (!m_mainAcctWaiting) {
            onMainAccountRefreshClicked();
        }
    });
    
    // 启动日志精简：如不需要可注释
    // addLogMessage("应用程序启动完成");

    // ===== 初始化高级筛选表格 =====
    if (m_advancedTable) {
        m_advancedTable->setColumnCount(2);
        QStringList headers;
        headers << "关键词" << "最低价";
        m_advancedTable->setHorizontalHeaderLabels(headers);
        m_advancedTable->horizontalHeader()->setStretchLastSection(false);
        m_advancedTable->horizontalHeader()->setSectionResizeMode(0, QHeaderView::Stretch);
        m_advancedTable->setColumnWidth(1, 60);
        
        // 禁用交替行颜色，确保表格使用默认白色背景
        m_advancedTable->setAlternatingRowColors(false);
        m_advancedTable->setRowCount(6);
        for (int r = 0; r < 6; ++r) {
            m_advancedTable->setItem(r, 0, createCenteredTableItem());
            m_advancedTable->setItem(r, 1, createCenteredTableItem());
        }
        // 始终可见
        m_advancedTable->setVisible(true);
        // 初始化表格颜色
        updateAdvancedTableColors();
        // 启用多选
        m_advancedTable->setSelectionMode(QAbstractItemView::ExtendedSelection);
        m_advancedTable->setSelectionBehavior(QAbstractItemView::SelectRows);
        
        // 右键菜单：增删行
        m_advancedTable->setContextMenuPolicy(Qt::CustomContextMenu);
        connect(m_advancedTable, &QTableWidget::customContextMenuRequested, this, [this](const QPoint &pos) {
            QMenu menu(this);
            QAction *insertAct = menu.addAction("插入行");
            QAction *deleteAct = menu.addAction("删除行");
            
            // 获取所有选中的行
            const auto selectedItems = m_advancedTable->selectedItems();
            QSet<int> selectedRows;
            for (const auto* item : selectedItems) {
                selectedRows.insert(item->row());
            }
            
            // 当前右键点击的行
            int clickedRow = m_advancedTable->rowAt(pos.y());
            
            // 如果没有选中行，但有点击行，则使用点击行
            if (selectedRows.isEmpty() && clickedRow >= 0) {
                selectedRows.insert(clickedRow);
            }
            
            // 添加修改额外排除词格式的选项
            QAction *editExclusionFormatAct = nullptr;
            
            // 只有当选中了至少一行且第一列有内容时，才显示此选项
            bool hasValidSelection = false;
            QString firstSelectedKeyword;
            
            for (const auto row : selectedRows) {
                const auto* item = m_advancedTable->item(row, 0);
                if (item && !item->text().trimmed().isEmpty()) {
                    hasValidSelection = true;
                    firstSelectedKeyword = item->text().trimmed();
                    break;
                }
            }
            
            if (hasValidSelection) {
                editExclusionFormatAct = menu.addAction("设置额外排除词");
            }
            
            QAction *sel = menu.exec(m_advancedTable->viewport()->mapToGlobal(pos));
            if (sel == insertAct) {
                int insertRow = clickedRow;
                if (insertRow < 0) insertRow = m_advancedTable->rowCount() - 1;
                m_advancedTable->insertRow(insertRow + 1);
                m_advancedTable->setItem(insertRow + 1, 0, createCenteredTableItem());
                m_advancedTable->setItem(insertRow + 1, 1, createCenteredTableItem());
                updateAdvancedTableColors();
            } else if (sel == deleteAct && !selectedRows.isEmpty()) {
                // 从大到小排序行号，以便从下往上删除
                QList<int> rowsToDelete = selectedRows.values();
                std::sort(rowsToDelete.begin(), rowsToDelete.end(), std::greater<int>());
                
                for (int row : rowsToDelete) {
                    if (m_advancedTable->rowCount() > 1) {
                        m_advancedTable->removeRow(row);
                    }
                }
                updateAdvancedTableColors();
            } else if (sel == editExclusionFormatAct && hasValidSelection) {
                // 修改额外排除词格式
                bool ok;
                
                // 获取当前所有选中的非空关键词
                QStringList selectedKeywords;
                for (int row : selectedRows) {
                    QTableWidgetItem* item = m_advancedTable->item(row, 0);
                    if (item && !item->text().trimmed().isEmpty()) {
                        selectedKeywords.append(item->text().trimmed());
                    }
                }
                
                // 获取默认的排除词部分
                QString excludePart = "暗 到 超 影 排 次 一"; // 默认排除词
                if (m_exclusionMapFormat.contains('/')) {
                    excludePart = m_exclusionMapFormat.section('/', 1);
                }
                
                // 构建新的格式字符串
                QString newFormat;
                
                // 如果只有一个关键词，直接使用简单格式
                if (selectedKeywords.size() == 1) {
                    newFormat = QInputDialog::getText(
                        this, 
                        "设置额外排除词", 
                        "输入格式 '关键词/排除词1 排除词2...':",
                        QLineEdit::Normal,
                        selectedKeywords.first() + "/" + excludePart,
                        &ok
                    );
                    
                    if (ok && !newFormat.isEmpty()) {
                        // 保留现有的其他关键词设置
                        if (m_exclusionMapFormat.contains(';')) {
                            // 解析现有设置
                            QStringList existingFormats = m_exclusionMapFormat.split(';', Qt::SkipEmptyParts);
                            QStringList updatedFormats;
                            bool replaced = false;
                            
                            // 检查是否已存在该关键词的设置
                            for (const QString &format : existingFormats) {
                                if (format.contains('/')) {
                                    QString keyword = format.section('/', 0, 0).trimmed();
                                    if (keyword == selectedKeywords.first()) {
                                        // 替换现有设置
                                        updatedFormats.append(newFormat);
                                        replaced = true;
                                    } else {
                                        // 保留其他关键词的设置
                                        updatedFormats.append(format);
                                    }
                                }
                            }
                            
                            // 如果是新增的关键词
                            if (!replaced) {
                                updatedFormats.append(newFormat);
                            }
                            
                            m_exclusionMapFormat = updatedFormats.join(";");
                        } else {
                            // 如果之前没有多关键词设置，检查是否需要合并
                            if (m_exclusionMapFormat.contains('/')) {
                                QString oldKeyword = m_exclusionMapFormat.section('/', 0, 0).trimmed();
                                if (oldKeyword != selectedKeywords.first()) {
                                    // 合并旧设置和新设置
                                    m_exclusionMapFormat = m_exclusionMapFormat + ";" + newFormat;
                                } else {
                                    // 替换旧设置
                                    m_exclusionMapFormat = newFormat;
                                }
                            } else {
                                m_exclusionMapFormat = newFormat;
                            }
                        }
                        
                        updateExclusionMap(m_exclusionMapFormat);
                        saveSettings();
                    }
                } 
                // 如果有多个关键词，则使用批量设置
                else if (selectedKeywords.size() > 1) {
                    // 构建默认格式
                    QStringList formatParts;
                    for (const QString &keyword : selectedKeywords) {
                        formatParts.append(keyword + "/" + excludePart);
                    }
                    
                    QString message = QString("为 %1 个关键词设置相同的排除词:").arg(selectedKeywords.size());
                    
                    QString excludeWords = QInputDialog::getText(
                        this, 
                        "批量设置排除词", 
                        message,
                        QLineEdit::Normal,
                        excludePart,
                        &ok
                    );
                    
                    if (ok && !excludeWords.isEmpty()) {
                        // 保存现有的其他关键词设置
                        QMap<QString, QString> existingSettings;
                        
                        if (m_exclusionMapFormat.contains(';') || m_exclusionMapFormat.contains('/')) {
                            QStringList existingFormats = m_exclusionMapFormat.split(';', Qt::SkipEmptyParts);
                            if (existingFormats.isEmpty() && m_exclusionMapFormat.contains('/')) {
                                existingFormats.append(m_exclusionMapFormat);
                            }
                            
                            // 解析现有设置
                            for (const QString &format : existingFormats) {
                                if (format.contains('/')) {
                                    QString keyword = format.section('/', 0, 0).trimmed();
                                    QString exclusion = format.section('/', 1).trimmed();
                                    if (!keyword.isEmpty() && !selectedKeywords.contains(keyword)) {
                                        existingSettings[keyword] = exclusion;
                                    }
                                }
                            }
                        }
                        
                        // 构建新的格式字符串，包括现有设置和新设置
                        QStringList newFormatParts;
                        
                        // 添加保留的现有设置
                        for (auto it = existingSettings.constBegin(); it != existingSettings.constEnd(); ++it) {
                            newFormatParts.append(it.key() + "/" + it.value());
                        }
                        
                        // 添加新设置
                        for (const QString &keyword : selectedKeywords) {
                            newFormatParts.append(keyword + "/" + excludeWords);
                        }
                        
                        m_exclusionMapFormat = newFormatParts.join(";");
                        updateExclusionMap(m_exclusionMapFormat);
                        saveSettings();
                    }
                }
            }
        });
        // 列1(最低价) 只允许数字
        m_advancedTable->setItemDelegateForColumn(1, new NumericDelegate(m_advancedTable));
    }

    // ===== Connect buttons to slots =====
    connect(m_loginButton, &QPushButton::clicked, this, &MainWindow::onLoginClicked);
    connect(m_batchLoginButton, &QPushButton::clicked, this, &MainWindow::onBatchLoginClicked);
    connect(m_refreshButton, &QPushButton::clicked, this, &MainWindow::onRefreshClicked);
    connect(m_stopButton, &QPushButton::clicked, this, &MainWindow::onStopClicked);
    if (m_loadAccountsButton) {
        connect(m_loadAccountsButton, &QPushButton::clicked, this, &MainWindow::onLoadAccountsClicked);
    }
    if (m_clearLogButton) {
        connect(m_clearLogButton, &QPushButton::clicked, this, &MainWindow::onClearLogClicked);
    }
    if (m_mainAccountRefreshButton) {
        connect(m_mainAccountRefreshButton, &QPushButton::clicked, this, &MainWindow::onMainAccountRefreshClicked);
    }

    // 右键菜单：清空日志
    if (m_logText) {
        m_logText->setContextMenuPolicy(Qt::CustomContextMenu);
        connect(m_logText, &QPlainTextEdit::customContextMenuRequested, this, [this](const QPoint &pos){
            QMenu menu(this);
            QAction *clearAct = menu.addAction("清空日志");
            connect(clearAct, &QAction::triggered, this, [this](){ m_logText->clear(); });
            QAction *toggleAct = menu.addAction(m_showOrderSummary ? "关闭订单摘要" : "显示订单摘要");
            connect(toggleAct, &QAction::triggered, this, [this](){
                m_showOrderSummary = !m_showOrderSummary;
                addLogMessage(m_showOrderSummary ? "已开启订单摘要" : "已关闭订单摘要");
            });
            menu.exec(m_logText->viewport()->mapToGlobal(pos));
        });
    }

    // ========== 账号列表表头与列宽 ==========
    if (m_accountTable) {
        // 更新表头文本（Designer 中也已改，但双保险）
        QStringList headers;
        headers << "序号" << "账号" << "密码" << "代理" << "Token" << "状态";
        m_accountTable->setColumnCount(headers.size());
        m_accountTable->setHorizontalHeaderLabels(headers);

        auto ha = m_accountTable->horizontalHeader();
        ha->setSectionResizeMode(QHeaderView::Fixed);
        m_accountTable->setColumnWidth(0, 0);   // 序号隐藏
        m_accountTable->setColumnHidden(0, true);
        m_accountTable->setColumnWidth(1, 90);  // 账号 (12位数字)
        m_accountTable->setColumnWidth(2, 70);  // 密码 (10位)
        m_accountTable->setColumnWidth(3, 60);  // 代理 (3汉字)
        m_accountTable->setColumnWidth(5, 70);  // 状态 (4汉字)
        // Token 列（4）填充余下
        ha->setStretchLastSection(false);
        ha->setSectionResizeMode(4, QHeaderView::Stretch);

        // 右键菜单：清空账号/代理列表
        m_accountTable->setContextMenuPolicy(Qt::CustomContextMenu);
        connect(m_accountTable, &QTableWidget::customContextMenuRequested, this, [this](const QPoint &pos){
            QMenu menu(this);
            QAction *clearAcc = menu.addAction("清空账号列表");
            connect(clearAcc, &QAction::triggered, this, [this](){
                {
                    QMutexLocker lk(&m_dataMutex);
                    m_accounts.clear();
                }
                updateAccountTable();
                addLogMessage("已清空账号列表");
                saveSettings();
            });
            menu.exec(m_accountTable->viewport()->mapToGlobal(pos));
        });
    }

    // ========== 新订单表格列配置 ==========
    if (m_orderTable) {
        QStringList oHeaders;
        oHeaders << "序号" << "订单ID" << "标题" << "价格" << "限时" << "发布者" << "区服" << "时间";
        m_orderTable->setColumnCount(oHeaders.size());
        m_orderTable->setHorizontalHeaderLabels(oHeaders);
        m_orderTable->setEditTriggers(QAbstractItemView::NoEditTriggers); // 不可编辑
        auto h = m_orderTable->horizontalHeader();
        h->setSectionResizeMode(QHeaderView::Fixed);
        m_orderTable->setColumnWidth(0, 0);   // 序号隐藏
        m_orderTable->setColumnHidden(0, true);
        m_orderTable->setColumnWidth(1, 0);   // 订单ID隐藏列宽0
        m_orderTable->setColumnHidden(1, true);
        m_orderTable->setColumnWidth(3, 60);  // 价格 (4位)
        m_orderTable->setColumnWidth(4, 40);  // 限时 (2汉字宽)
        m_orderTable->setColumnWidth(5, 120); // 发布者 (8汉字)
        m_orderTable->setColumnWidth(6, 80);  // 区服 (5汉字)
        m_orderTable->setColumnWidth(7, 80);  // 时间 (8数字)
        h->setStretchLastSection(false);
        h->setSectionResizeMode(2, QHeaderView::Stretch); // 标题列自适应

        // ------ 将"区服"列(逻辑索引6)移动到"价格"(3)右侧 ------
        {
            int priceVis = h->visualIndex(3);
            int zoneVis  = h->visualIndex(6);
            if (zoneVis != -1 && priceVis != -1 && zoneVis > priceVis + 1) {
                h->moveSection(zoneVis, priceVis + 1);
            }
        }

        // 行高缩小：显示更多行
        auto vo = m_orderTable->verticalHeader();
        vo->setSectionResizeMode(QHeaderView::Fixed);
        vo->setDefaultSectionSize(18);

        // 右键菜单：关注/取消关注/拉黑
        m_orderTable->setContextMenuPolicy(Qt::CustomContextMenu);
        connect(m_orderTable, &QTableWidget::customContextMenuRequested,
                this, &MainWindow::onOrderTableContextMenu);

        // 双击行 -> 手动尝试接单
        connect(m_orderTable, &QTableWidget::cellDoubleClicked,
                this, [this](int row, int /*column*/){
            if (row < 0 || row >= m_orderTable->rowCount()) return;
            int idx = m_orders.size() - 1 - row; // 表格显示反向
            if (idx < 0 || idx >= m_orders.size()) return;
            const OrderInfo &ord = m_orders[idx];
            if (ord.orderId.isEmpty()) return;

            QJsonObject obj;
            obj["SerialNo"] = ord.orderId;
            obj["Stamp"] = "0";
            obj["Title"] = ord.title;
            obj["Price"] = ord.price.toDouble();
            obj["UserID"] = ord.publishUserId;
            obj["TimeLimit"] = ord.timeLimit;
            obj["Create"] = ord.createUser;
            obj["Zone"] = ord.zoneServer;
            attemptAcceptOrder(obj, true);
        });
    }

    // ========== 抢单结果表格列配置 ==========
    if (m_resultTable) {
        QStringList rHeaders;
        rHeaders << "序号" << "订单ID" << "标题" << "价格" << "限时" << "发布者" << "区服" << "时间" << "抢单结果" << "UID";
        m_resultTable->setColumnCount(rHeaders.size());
        m_resultTable->setHorizontalHeaderLabels(rHeaders);
        auto hr = m_resultTable->horizontalHeader();
        hr->setSectionResizeMode(QHeaderView::Fixed);
        m_resultTable->setColumnWidth(0, 0);
        m_resultTable->setColumnHidden(0, true); // 序号
        m_resultTable->setColumnWidth(1, 0);
        m_resultTable->setColumnHidden(1, true); // 订单ID 隐藏显示但保留数据
        m_resultTable->setColumnWidth(3, 60);  // 价格
        m_resultTable->setColumnWidth(4, 40);  // 限时
        m_resultTable->setColumnWidth(5, 120); // 发布者
        m_resultTable->setColumnWidth(6, 80);  // 区服
        m_resultTable->setColumnWidth(7, 80);  // 时间
        m_resultTable->setColumnWidth(8, 120); // 结果列
        m_resultTable->setColumnWidth(9, 0);   // UID隐藏
        m_resultTable->setColumnHidden(9, true);
        hr->setStretchLastSection(false);
        hr->setSectionResizeMode(2, QHeaderView::Stretch);

        // ------ 将"区服"列(逻辑索引6)移动到"价格"(3)右侧 ------
        {
            int priceVis = hr->visualIndex(3);
            int zoneVis  = hr->visualIndex(6);
            if (zoneVis != -1 && priceVis != -1 && zoneVis > priceVis + 1) {
                hr->moveSection(zoneVis, priceVis + 1);
            }
        }

        // 行高缩小：显示更多行
        auto vr = m_resultTable->verticalHeader();
        vr->setSectionResizeMode(QHeaderView::Fixed);
        vr->setDefaultSectionSize(18);  // px，可根据需要调整

        // 不可编辑
        m_resultTable->setEditTriggers(QAbstractItemView::NoEditTriggers);

        // 右键菜单同新订单表
        m_resultTable->setContextMenuPolicy(Qt::CustomContextMenu);
        connect(m_resultTable, &QTableWidget::customContextMenuRequested,
                this, &MainWindow::onOrderTableContextMenu);

        // 双击结果表行再次尝试抢单（可能因失败想再试）
        connect(m_resultTable, &QTableWidget::cellDoubleClicked,
                this, [this](int row, int /*column*/){
            if (row<0||row>=m_resultTable->rowCount()) return;
            // 取订单ID列在隐藏列1
            QString orderId = m_resultTable->item(row, RES_ORDERID)?m_resultTable->item(row, RES_ORDERID)->text():"";
            if (orderId.isEmpty()) return;

            // 构造简要 QJsonObject，只需 SerialNo
            QJsonObject obj; obj["SerialNo"] = orderId; obj["Stamp"]="0";
            QString title = m_resultTable->item(row, RES_TITLE)?m_resultTable->item(row, RES_TITLE)->text():"";
            QString priceStr = m_resultTable->item(row, RES_PRICE)?m_resultTable->item(row, RES_PRICE)->text():"";
            bool ok=false; double priceVal = priceStr.toDouble(&ok);
            if(ok) obj["Price"] = priceVal; else obj["Price"] = priceStr;
            QString create = m_resultTable->item(row, RES_CREATOR)?m_resultTable->item(row, RES_CREATOR)->text():"";
            QString zone = m_resultTable->item(row, RES_ZONE)?m_resultTable->item(row, RES_ZONE)->text():"";
            QString timelimit = m_resultTable->item(row, RES_TIMELIMIT)?m_resultTable->item(row, RES_TIMELIMIT)->text():"";
            if(!timelimit.isEmpty()) obj["TimeLimit"] = timelimit;
            if(!title.isEmpty()) obj["Title"] = title;
            if(!create.isEmpty()) obj["Create"] = create;
            if(!zone.isEmpty()) obj["Zone"] = zone;
            QString uid = m_resultTable->item(row, RES_UID)?m_resultTable->item(row, RES_UID)->text():"";
            if(!uid.isEmpty()) obj["UserID"] = uid;
            attemptAcceptOrder(obj, true);
        });
    }

    // ========== 如果Designer未给combobox项设置userData，则重新填充带ID的列表 ==========
    if (m_gameCombo) {
        bool needInit = false;
        for (int i = 0; i < m_gameCombo->count(); ++i) {
            if (!m_gameCombo->itemData(i).isValid()) { needInit = true; break; }
        }
        if (needInit) {
            m_gameCombo->clear();
            struct { const char *name; const char *id; } games[] = {
                {"王者荣耀", "107"}, {"和平精英", "124"}, {"火影忍者", "110"}, {"英雄联盟手游", "156"},
                {"无畏契约", "314"}, {"星穹铁道", "313"}, {"金铲铲之战", "239"}, {"CSGO", "117"},
                {"逆战", "115"}, {"原神", "190"}, {"鸣潮", "387"}, {"部落冲突", "160"}
            };
            for (auto &g : games) {
                m_gameCombo->addItem(QString::fromUtf8(g.name), QString::fromUtf8(g.id));
            }
        }
    }

    // ========== 加载配置 ==========
    loadSettings();

    // ========== 自动保存信号 ==========
    auto connectSave = [this](auto signal, auto sender){ connect(sender, signal, this, &MainWindow::saveSettings); };
    connectSave(&QLineEdit::editingFinished, m_usernameEdit);
    connectSave(&QLineEdit::editingFinished, m_passwordEdit);
    connect(m_gameCombo, &QComboBox::currentIndexChanged, this, &MainWindow::saveSettings);
    connect(m_pageSizeSpin, qOverload<int>(&QSpinBox::valueChanged), this, &MainWindow::saveSettings);
    connect(m_priceFilterCheck, &QCheckBox::toggled, this, &MainWindow::saveSettings);
    connectSave(&QLineEdit::editingFinished, m_priceMinEdit);
    connectSave(&QLineEdit::editingFinished, m_priceMaxEdit);
    connect(m_focusOnlyCheck, &QCheckBox::toggled, this, &MainWindow::saveSettings);
    connect(m_advancedModeCheck, &QCheckBox::toggled, this, [this](bool checked){
        if (!m_excludeKeywordEdit) return;
        if (checked) {
            m_normalExcludeKeywords = m_excludeKeywordEdit->text();
            m_excludeKeywordEdit->setText(m_advancedExcludeKeywords);
        } else {
            m_advancedExcludeKeywords = m_excludeKeywordEdit->text();
            m_excludeKeywordEdit->setText(m_normalExcludeKeywords);
        }
        if (m_acceptTargetSpin) m_acceptTargetSpin->setEnabled(checked);
        
        // 仅在非锁定状态才标记需要重新优化关键词过滤
        if (!m_filterLocked) {
            m_filterOptimized = false;
        }
        
        saveSettings();
    });
    connectSave(&QLineEdit::editingFinished, m_filterKeywordEdit);
    connectSave(&QLineEdit::editingFinished, m_excludeKeywordEdit);
    connectSave(&QLineEdit::editingFinished, m_payPasswordEdit);
    if (m_advancedTable) {
        connect(m_advancedTable, &QTableWidget::cellChanged, this, [this](int, int) {
            saveSettings();
            updateAdvancedTableColors();
        });
    }
    if (m_intervalCheck) connect(m_intervalCheck, &QCheckBox::toggled, this, &MainWindow::saveSettings);
    if (m_intervalSpin) {
        connect(m_intervalSpin, qOverload<int>(&QSpinBox::valueChanged), this, &MainWindow::saveSettings);
    }

    // 主账号专用间隔设置
    if (m_mainAcctIntervalSpin) {
        connect(m_mainAcctIntervalSpin, qOverload<int>(&QSpinBox::valueChanged), this, &MainWindow::saveSettings);
        connect(m_mainAcctIntervalSpin, qOverload<int>(&QSpinBox::valueChanged), this, [this](int val){
            if (m_mainAcctTimer->isActive()) {
                if (val > 0) {
                    m_mainAcctTimer->setInterval(val);
                    addLogMessage(QString("主账号刷新间隔已更新为: %1ms").arg(val));
                } else {
                    m_mainAcctTimer->stop();
                    addLogMessage("主账号自动刷新已停止（间隔设为0）");
                }
            }
        });
    }

    // 初始化指纹选择控件
    if (m_fingerprintCombo) {
        m_fingerprintCombo->addItem("🤖 夸克浏览器", static_cast<int>(OrderAPI::QUARK_FINGERPRINT));
        m_fingerprintCombo->addItem("💬 微信浏览器", static_cast<int>(OrderAPI::WECHAT_FINGERPRINT));
        m_fingerprintCombo->setCurrentIndex(0); // 默认选择夸克

        connect(m_fingerprintCombo, qOverload<int>(&QComboBox::currentIndexChanged), this, [this](int index) {
            if (m_api) {
                OrderAPI::MainAccountFingerprint fingerprint =
                    static_cast<OrderAPI::MainAccountFingerprint>(m_fingerprintCombo->itemData(index).toInt());
                m_api->setMainAccountFingerprint(fingerprint);
                saveSettings();
            }
        });
    }

    // 网络引擎选择控件已删除，默认使用UltraFastTLS

    // 统一让所有 QPushButton 尺寸贴合文本
    for (QPushButton *btn : this->findChildren<QPushButton*>()) {
        btn->setFixedSize(btn->sizeHint());
    }

    // ===== 新增：成功接单自动停止 =====
    // 由 Designer 提供控件：acceptTargetSpin, stopAfterTargetCheck, label
    m_acceptTargetSpin     = findChild<QSpinBox*>("acceptTargetSpin");
    m_stopAfterTargetCheck = findChild<QCheckBox*>("stopAfterTargetCheck");

    if (m_acceptTargetSpin) {
        connect(m_acceptTargetSpin, qOverload<int>(&QSpinBox::valueChanged), this, [this](int){ saveSettings(); });
    }

    if (m_stopAfterTargetCheck) {
        if (m_acceptTargetSpin)
            m_acceptTargetSpin->setEnabled(m_stopAfterTargetCheck->isChecked());
        connect(m_stopAfterTargetCheck, &QCheckBox::toggled, this, [this](bool checked){
            if (m_acceptTargetSpin) m_acceptTargetSpin->setEnabled(checked);
            saveSettings();
        });
    }

    // 重新加载设置，确保新创建的控件应用持久化值
    loadSettings();

    if (m_excludeKeywordEdit) {
        connect(m_excludeKeywordEdit, &QLineEdit::editingFinished, this, [this](){
            if (m_advancedModeCheck && m_advancedModeCheck->isChecked())
                m_advancedExcludeKeywords = m_excludeKeywordEdit->text();
            else
                m_normalExcludeKeywords = m_excludeKeywordEdit->text();
                
            // 仅在非锁定状态才标记需要重新优化关键词过滤
            if (!m_filterLocked) {
                m_filterOptimized = false;
            }
        });
    }

    // 自定义右键菜单：仅保留"全选"和"复制"
    if (m_excludeKeywordEdit) {
        m_excludeKeywordEdit->setContextMenuPolicy(Qt::CustomContextMenu);
        connect(m_excludeKeywordEdit, &QLineEdit::customContextMenuRequested,
                this, [this](const QPoint &pos){
            if (!m_excludeKeywordEdit) return;
            QMenu menu(this);
            QAction *selCopy = menu.addAction("全选复制");
            connect(selCopy, &QAction::triggered, this, [this](){
                if (!m_excludeKeywordEdit) return;
                m_excludeKeywordEdit->selectAll();
                m_excludeKeywordEdit->copy();
            });
            menu.exec(m_excludeKeywordEdit->mapToGlobal(pos));
        });
    }

    // 连接调试日志信号
    connect(m_api, &OrderAPI::debugLog, this, &MainWindow::addLogMessage);

    // 连接异步登录管理器信号
    connect(m_asyncLoginManager, &AsyncLoginManager::accountLoginCompleted,
            this, &MainWindow::onAsyncAccountLoginCompleted);
    connect(m_asyncLoginManager, &AsyncLoginManager::batchLoginCompleted,
            this, &MainWindow::onAsyncBatchLoginCompleted);
    connect(m_asyncLoginManager, &AsyncLoginManager::progressUpdated,
            this, &MainWindow::onAsyncLoginProgressUpdated);

    // 创建分组统计表格
    m_groupStatsTable = findChild<QTableWidget*>("groupStatsTable");
    if (m_groupStatsTable) {
        // 设置表格属性
        m_groupStatsTable->setColumnCount(2);
        QStringList headers;
        headers << "分组名称" << "关键词数量";
        m_groupStatsTable->setHorizontalHeaderLabels(headers);
        m_groupStatsTable->setEditTriggers(QAbstractItemView::NoEditTriggers); // 不可编辑
        m_groupStatsTable->setAlternatingRowColors(true); // 交替行颜色
        m_groupStatsTable->horizontalHeader()->setSectionResizeMode(0, QHeaderView::Stretch); // 第一列自适应宽度
        m_groupStatsTable->horizontalHeader()->setStretchLastSection(false);
        m_groupStatsTable->setColumnWidth(1, 80); // 数量列固定宽度
    }

    // 设置常驻工作线程
    setupWorkerThreads();
    
    // 设置默认的手动排除格式
    if (m_extraExclusionWorker) {
        m_extraExclusionWorker->setExclusionMap("周胜/暗 到 超 影 排 次 一");
    }
}

MainWindow::~MainWindow()
{
    // 停止并删除工作线程
    if (m_extraExclusionThread) {
        m_extraExclusionThread->quit();
        m_extraExclusionThread->wait();
        delete m_extraExclusionWorker; // 工作线程对象需要手动删除
    }
    
    // 不再需要线程退出和等待
    // if (m_apiThread) {
    //     m_apiThread->quit();
    //     m_apiThread->wait();
    // }
}

void MainWindow::onLoginClicked()
{
    // 清除所有控件的焦点，避免焦点跳转
    this->setFocus();

    QString username = m_usernameEdit->text();
    QString password = m_passwordEdit->text();

    if (username.isEmpty() || password.isEmpty()) {
        QMessageBox::warning(this, "警告", "请输入用户名和密码");
        return;
    }

    m_loginButton->setEnabled(false);
    addLogMessage("主账号登录: " + username);

    m_api->mainAccountLogin(username, password, [this, username, password](const QString& result){
        m_loginButton->setEnabled(true);
        QJsonDocument doc = QJsonDocument::fromJson(result.toUtf8());
        bool success = false;
        QString message, token, userId, uid, errMsg;
        if (doc.isObject()) {
            QJsonObject obj = doc.object();
            success = obj.value("Result").toInt() == 1;
            message = obj.value("Err").toString();
            token = obj.value("Token").toString();
            userId = QString::number(obj.value("UserID").toInt());
            errMsg = obj.value("Err").toString();
            uid = obj.contains("UID") ? obj.value("UID").toVariant().toString() : QString();
        } else {
            // addLogMessage(QString("登录原始返回: %1").arg(result.left(300)));
            message = "登录响应解析失败";
        }
        if (success) {
            m_mainAccount = {m_usernameEdit->text(), m_passwordEdit->text(), "", 0, "", "", token, userId, true, uid};
            // 可在界面高亮显示主账号信息
            addLogMessage("主账号登录成功");
            m_api->mainAccountGetUserInfo(token, userId);
            int interval = m_mainAcctIntervalSpin ? m_mainAcctIntervalSpin->value() : 5000; // 默认5秒间隔
            if (interval > 0) {
                m_mainAcctTimer->start(interval);
                addLogMessage(QString("主账号自动刷新已启动，间隔: %1ms").arg(interval));
            } else {
                addLogMessage("主账号自动刷新未启动（间隔为0）");
            }
            onMainAccountRefreshClicked();
            m_mainAccount.uid = uid;
        } else {
            m_mainAccount = MainAccountInfo();
            QMessageBox::warning(this, "登录失败", message);
        }
        // 可刷新主账号信息显示
    });
}

void MainWindow::onBatchLoginClicked()
{
    if (m_accounts.isEmpty()) {
        addLogMessage("请先加载账号");
        return;
    }

    if (m_isBatchLogin) {
        addLogMessage("批量登录进行中，请等待...");
        return;
    }

    // 准备登录任务
    QList<LoginTask> loginTasks;
    for (int i = 0; i < m_accounts.size(); ++i) {
        AccountInfo &account = m_accounts[i];
        if (account.isLoggedIn) {
            continue; // 跳过已登录的账号
        }

        // 创建独立的API实例
        if (!account.api) {
            account.api = new OrderAPI(this);

            // 连接调试日志
            connect(account.api, &OrderAPI::debugLog, this, [this, username = account.username](const QString& msg) {
                // 🔧 显示所有重要的调试信息
                if (msg.contains("登录") || msg.contains("成功") || msg.contains("失败") ||
                    msg.contains("🔧") || msg.contains("🔗") || msg.contains("🚀") ||
                    msg.contains("📡") || msg.contains("📥") || msg.contains("⚠️")) {
                    addLogMessage(QString("[%1] %2").arg(username).arg(msg));
                }
            });

            // 🔧 连接刷新相关信号（重要！）- 使用Qt::UniqueConnection防止重复连接
            connect(account.api, &OrderAPI::orderRefreshResult, this, &MainWindow::onOrderRefreshResult, Qt::UniqueConnection);
            connect(account.api, &OrderAPI::networkError, this, &MainWindow::onNetworkError, Qt::UniqueConnection);
            connect(account.api, &OrderAPI::orderAcceptResult, this, &MainWindow::onOrderAcceptResult, Qt::UniqueConnection);

            // 连接关注/拉黑相关信号
            connect(account.api, &OrderAPI::focusUserResult, this, [this, username = account.username](bool ok, const QString &msg, const QString &fid){
                addLogMessage(QString("子账号[%1] 关注结果[%2]: %3")
                               .arg(username)
                               .arg(fid)
                               .arg(ok ? (msg.isEmpty()?"成功":msg) : (QString("失败:")+msg)));
            });
            connect(account.api, &OrderAPI::cancelFocusUserResult, this, [this, username = account.username](bool ok, const QString &msg, const QString &fid){
                addLogMessage(QString("子账号[%1] 取消关注结果[%2]: %3")
                               .arg(username)
                               .arg(fid)
                               .arg(ok ? (msg.isEmpty()?"成功":msg) : (QString("失败:")+msg)));
            });
            connect(account.api, &OrderAPI::addBlackResult, this, [this, username = account.username](bool ok, const QString &msg, const QString &uid){
                addLogMessage(QString("子账号[%1] 拉黑结果[%2]: %3")
                               .arg(username)
                               .arg(uid)
                               .arg(ok ? (msg.isEmpty() ? "成功" : msg) : (QString("失败:") + msg)));
            });
            connect(account.api, &OrderAPI::removeBlackResult, this, [this, username = account.username](bool ok, const QString &msg, const QString &uid){
                addLogMessage(QString("子账号[%1] 取消拉黑结果[%2]: %3")
                               .arg(username)
                               .arg(uid)
                               .arg(ok ? (msg.isEmpty() ? "成功" : msg) : (QString("失败:") + msg)));
            });
        }

        // 构建代理字符串
        QString proxyString;
        if (!account.proxyHost.isEmpty() && account.proxyPort > 0) {
            proxyString = QString("%1:%2").arg(account.proxyHost).arg(account.proxyPort);
            if (!account.proxyUser.isEmpty() && !account.proxyPass.isEmpty()) {
                proxyString += QString(":%1:%2").arg(account.proxyUser).arg(account.proxyPass);
            }
        }

        // 创建登录任务
        LoginTask task;
        task.accountIndex = i;
        task.username = account.username;
        task.password = account.password;
        task.proxyString = proxyString;
        task.api = account.api;
        task.callback = [this](int accountIndex, bool success, const QString& message) {
            // 这个回调会在主线程中执行
            Q_UNUSED(accountIndex)
            Q_UNUSED(success)
            Q_UNUSED(message)
            // 具体处理在 onAsyncAccountLoginCompleted 中
        };

        loginTasks.append(task);
    }

    if (loginTasks.isEmpty()) {
        addLogMessage("所有账号已登录");
        return;
    }

    addLogMessage(QString("开始异步登录 %1 个账号").arg(loginTasks.size()));

    // 设置登录状态
    m_isBatchLogin = true;

    // 暂停主账号刷新
    m_mainAcctTimerWasActive = m_mainAcctTimer && m_mainAcctTimer->isActive();
    if (m_mainAcctTimerWasActive) {
        m_mainAcctTimer->stop();
    }

    // 启动异步批量登录
    m_asyncLoginManager->startBatchLogin(loginTasks);
}

// 异步登录相关槽函数
void MainWindow::onAsyncAccountLoginCompleted(const LoginResult& result)
{
    if (result.accountIndex < 0 || result.accountIndex >= m_accounts.size()) {
        return;
    }

    AccountInfo &account = m_accounts[result.accountIndex];

    if (result.success) {
        account.isLoggedIn = true;
        account.token = result.token;
        account.userId = result.userId;
        account.uid = result.uid;
        addLogMessage(QString("账号 %1 登录成功").arg(account.username));
    } else {
        account.isLoggedIn = false;
        addLogMessage(QString("账号 %1 登录失败: %2").arg(account.username).arg(result.message));
    }

    updateAccountTable();
}

void MainWindow::onAsyncBatchLoginCompleted(int successful, int total)
{
    // 重置状态
    m_isBatchLogin = false;
    m_pendingLoginCount = 0;

    addLogMessage(QString("批量登录完成: %1/%2").arg(successful).arg(total));

    // 🚀 预热子账号连接（提升首次刷新速度）
    addLogMessage("🔧 开始预热子账号连接...");
    for (auto& account : m_accounts) {
        if (account.isLoggedIn && account.api) {
            // 异步预热连接，不阻塞主线程
            QTimer::singleShot(100, [&account]() {
                // 触发一次轻量级的连接建立
                if (account.api) {
                    // 这里可以调用一个轻量级的API来建立连接
                    // 暂时先记录日志
                }
            });
        }
    }

    // 恢复主账号自动刷新
    if (m_mainAcctTimerWasActive && m_mainAcctTimer && !m_mainAcctTimer->isActive()) {
        int interval = m_mainAcctIntervalSpin ? m_mainAcctIntervalSpin->value() : 5000;
        if (interval > 0) {
            m_mainAcctTimer->start(interval);
            addLogMessage(QString("已恢复主账号自动刷新，间隔: %1ms").arg(interval));
        }
    }

    updateAccountTable();
}

void MainWindow::onAsyncLoginProgressUpdated(int completed, int total)
{
    // 可以在这里更新进度条或状态显示
    Q_UNUSED(completed)
    Q_UNUSED(total)
}


void MainWindow::finalizeBatchLogin()
{


    // 清理所有 loginSent 标志，确保状态一致
    for (auto &account : m_accounts) {
        if (account.loginSent) {
            account.loginSent = false;
            addLogMessage(QString("清理账号 %1 的登录标志").arg(account.username));
        }
    }

    // 重置状态
    m_isBatchLogin = false;
    m_pendingLoginCount = 0;

    // 统计登录结果
    int loggedInCount = 0;
    for (const auto &account : m_accounts) {
        if (account.isLoggedIn) {
            loggedInCount++;
        }
    }

    addLogMessage(QString("批量登录完成: %1/%2").arg(loggedInCount).arg(m_accounts.size()));

    // 恢复主账号自动刷新
    if (m_mainAcctTimerWasActive && m_mainAcctTimer && !m_mainAcctTimer->isActive()) {
        int interval = m_mainAcctIntervalSpin ? m_mainAcctIntervalSpin->value() : 5000;
        if (interval > 0) {
            m_mainAcctTimer->start(interval);
            addLogMessage(QString("已恢复主账号自动刷新，间隔: %1ms").arg(interval));
        }
    }

    updateAccountTable();
}


void MainWindow::stopBatchLogin()
{
    if (!m_isBatchLogin) {
        return;
    }



    // 清除所有账号的 loginSent 标志
    int canceledCount = 0;
    for (auto &account : m_accounts) {
        if (account.loginSent) {
            account.loginSent = false;
            canceledCount++;
            addLogMessage(QString("取消账号 %1 的登录请求").arg(account.username));
        }
    }

    // 重置批量登录状态
    m_isBatchLogin = false;
    m_pendingLoginCount = 0;



    // 恢复主账号定时器
    if (m_mainAcctTimerWasActive && m_mainAcctTimer && !m_mainAcctTimer->isActive()) {
        int interval = m_mainAcctIntervalSpin ? m_mainAcctIntervalSpin->value() : 5000;
        if (interval > 0) {
            m_mainAcctTimer->start(interval);
            addLogMessage(QString("已恢复主账号自动刷新，间隔: %1ms").arg(interval));
        }
    }

    updateAccountTable();
}

// 移除了静态名单相关函数，恢复原有逻辑

void MainWindow::onRefreshClicked()
{
    if (m_isRefreshing) {
        addLogMessage("已在刷新中，无需重复点击");
        return;
    }
    
    // 优化并锁定关键词结构，提高抢单性能
    optimizeKeywordFilters();
    m_filterLocked = true;
    
    // 若主账号定时器在运行，先停止；本轮批量刷新结束后不再自动恢复
    if (m_mainAcctTimer && m_mainAcctTimer->isActive()) {
        m_mainAcctTimer->stop();
    }

    // 如果主账号尚未登录，则自动登录后再开始批量刷新
    if (m_mainAccount.token.isEmpty() || m_mainAccount.userId.isEmpty()) {
        QString username = m_usernameEdit->text().trimmed();
        QString password = m_passwordEdit->text().trimmed();
        if (username.isEmpty() || password.isEmpty()) {
            QMessageBox::warning(this, "警告", "主账号未登录且用户名/密码为空，无法自动登录");
            return;
        }
        addLogMessage("主账号未登录，自动登录中(TLS伪装)...");
        m_api->mainAccountLogin(username, password, [this](const QString &result){
            QJsonDocument doc = QJsonDocument::fromJson(result.toUtf8());
            bool success = false;
            QString token, userId, uid, errMsg;   // 新增 uid
            if (doc.isObject()) {
                QJsonObject obj = doc.object();
                success = obj.value("Result").toInt() == 1;
                token = obj.value("Token").toString();
                userId = QString::number(obj.value("UserID").toInt());
                errMsg = obj.value("Err").toString();
                uid = obj.contains("UID") ? obj.value("UID").toVariant().toString() : QString();
            }
            if (!success) {
                addLogMessage("自动登录主账号失败: " + errMsg);
                return;
            }
            m_mainAccount = {m_usernameEdit->text(), m_passwordEdit->text(), "", 0, "", "", token, userId, true, uid};
            addLogMessage("主账号自动登录成功");

            m_api->mainAccountGetUserInfo(token, userId);
            startBatchRefreshLoop();
        });
    } else {
        startBatchRefreshLoop();
    }
}

void MainWindow::onStopClicked()
{
    // 停止操作开始


    stopBatchLogin();

    // ========== 停止刷新 ==========
    m_isRefreshing = false;

    // 解锁关键词结构，恢复动态模式
    m_filterLocked = false;

    stopRefreshTimer();
    addLogMessage("停止刷新");

    // ========== 恢复主账号定时器 ==========
    if (m_mainAcctTimerWasActive && m_mainAcctTimer && !m_mainAcctTimer->isActive()) {
        int interval = m_mainAcctIntervalSpin ? m_mainAcctIntervalSpin->value() : 5000;
        if (interval > 0) {
            m_mainAcctTimer->start(interval);
            addLogMessage(QString("已恢复主账号自动刷新，间隔: %1ms").arg(interval));
        }
    }
}

void MainWindow::onLoadAccountsClicked()
{
    loadAccountsFromFile();
}

void MainWindow::onClearLogClicked()
{
    m_logText->clear();
}

void MainWindow::onLoginResult(bool success, const QString &message, const QString &/*token*/)
{
    // 登录结果处理（参数已优化为只包含实际使用的）
    if (success) {
        // 登录成功，不输出重复主账号成功日志
    } else {
        addLogMessage("主账号登录失败: " + message);
    }
}

void MainWindow::onOrderRefreshResult(bool success, const QString &message, const QJsonArray &orders, int recordCount)
{
    qDebug() << "🔍 [DEBUG] onOrderRefreshResult 开始 - success:" << success << "orders.size():" << orders.size();

    // 🔒 添加异常处理防止闪退
    try {
        qDebug() << "🔍 [DEBUG] onOrderRefreshResult 进入try块";

        // onOrderRefreshResult 收到信号

        // 恢复原始的简单逻辑，移除复杂的过滤

        m_waitingRefresh = false; // 当前账号处理完毕，可刷新下一个
        bool isMain = m_isMainAccountRefreshing;
        if (isMain) m_mainAcctWaiting = false;

        qDebug() << "🔍 [DEBUG] onOrderRefreshResult 状态重置完成 - isMain:" << isMain;
    // 一律使用本地时间并带毫秒，避免服务器时间缺失且格式统一
    QString netTime = QDateTime::currentDateTime().toString("hh:mm:ss.zzz");

    QString label = m_currentRefreshingLabel.isEmpty() ? "" : (m_currentRefreshingLabel + " ");
    QString statLine;
    if (m_showOrderSummary && orders.size() > 0) {
        QStringList sampleTitles;
        for (int i = 0; i < orders.size() && sampleTitles.size() < 3; ++i) {
            QJsonObject obj = orders.at(i).toObject();
            QString t = obj.value("Title").toString();
            if (!t.isEmpty()) sampleTitles << t;
        }
        QString joined = sampleTitles.join(" | ");
        statLine = QString("%1 %2%3").arg(netTime).arg(label).arg(joined);
    } else {
        statLine = QString("%1 %2获取%3条订单 总%4条")
                       .arg(netTime)
                       .arg(label)
                       .arg(orders.size())
                       .arg(recordCount);
    }

    if (!orders.isEmpty() || (m_focusOnlyCheck && m_focusOnlyCheck->isChecked())) {
        m_logText->appendPlainText(statLine);
        QTextCursor cursor = m_logText->textCursor();
        cursor.movePosition(QTextCursor::End);
        m_logText->setTextCursor(cursor);
    }

    if (!success) {
        qDebug() << "🔍 [DEBUG] 刷新失败，准备返回";
        addLogMessage("刷新订单失败: " + message);
        return;
    }

    qDebug() << "🔍 [DEBUG] 刷新成功，准备进入后台解析";
    
    // ===== 后台解析：将耗时逻辑移出主线程 =====
    {
        // -------- 多账号解析入口（线程安全版本）--------
        QString key;
        OrderAPI *apiSender = qobject_cast<OrderAPI*>(sender());

        qDebug() << "🔍 [DEBUG] sender()检查 - apiSender:" << (void*)apiSender;

        // 🔒 线程安全检查
        if (!apiSender) {
            qDebug() << "🔍 [DEBUG] apiSender为空，返回";
            addLogMessage("⚠️ 刷新回调发送者为空，跳过处理");
            return;
        }

        if (apiSender == m_api) {
            key = "main";
            qDebug() << "🔍 [DEBUG] 识别为主账号";
        } else {
            qDebug() << "🔍 [DEBUG] 开始查找子账号 - m_accounts.size():" << m_accounts.size();
            // 🔒 使用互斥锁保护账号数组访问
            QMutexLocker locker(&m_dataMutex);
            bool found = false;
            for (int i = 0; i < m_accounts.size(); ++i) {
                const auto &acc = m_accounts[i];
                qDebug() << "🔍 [DEBUG] 检查账号" << i << "- api:" << (void*)acc.api << "userId:" << acc.userId;
                if (acc.api == apiSender) {
                    key = acc.userId;
                    found = true;
                    qDebug() << "🔍 [DEBUG] 找到匹配账号 - userId:" << key;
                    break;
                }
            }
            if (!found) {
                qDebug() << "🔍 [DEBUG] 未找到匹配的子账号API";
                addLogMessage("⚠️ 未找到对应的子账号API，跳过处理");
                return;
            }
        }
auto &watcher = m_watcherMap[key];
if (watcher && watcher->isRunning()) {
    m_pendingMap[key].enqueue(orders);   // 排队
    return;
}
if (!watcher) {
    watcher = new QFutureWatcher<RefreshParseResult>(this);
    connect(watcher, &QFutureWatcherBase::finished,
            this, &MainWindow::onAccountParseFinished);
    watcher->setProperty("acctKey", key);
}

bool firstBatch = m_seenOrderIds.isEmpty();
QSet<QString> seenCopy;
QHash<QString,double> priceCopy;
{ QMutexLocker lk(&m_dataMutex);  seenCopy = m_seenOrderIds; priceCopy = m_lastPrice; }

        qDebug() << "🔍 [DEBUG] 准备启动QtConcurrent - key:" << key << "orders.size():" << orders.size();

        QJsonArray ordersCopy = orders;
        auto fut = QtConcurrent::run([ordersCopy, firstBatch, seenCopy,
                                      priceCopy, key]() {
            qDebug() << "🔍 [DEBUG] QtConcurrent线程开始 - key:" << key;
            auto result = parseOrders(ordersCopy, firstBatch, key, seenCopy, priceCopy);
            qDebug() << "🔍 [DEBUG] QtConcurrent线程完成 - key:" << key;
            return result;
        });
        watcher->setFuture(fut);

        qDebug() << "🔍 [DEBUG] QtConcurrent已启动，等待完成";
        }
        // 解析在后台进行，主线程返回
        return;

    } catch (const std::exception& e) {
        // 🚨 捕获异常防止闪退
        qDebug() << "🔍 [DEBUG] 捕获std::exception:" << e.what();
        addLogMessage(QString("⚠️ 刷新结果处理异常: %1").arg(e.what()));
        m_waitingRefresh = false;
        continueAfterParse();
    } catch (...) {
        // 🚨 捕获所有异常
        qDebug() << "🔍 [DEBUG] 捕获未知异常";
        addLogMessage("⚠️ 刷新结果处理发生未知异常");
        m_waitingRefresh = false;
        continueAfterParse();
    }

    qDebug() << "🔍 [DEBUG] onOrderRefreshResult 完成";
}

void MainWindow::onNetworkError(const QString &error)
{
    try {
        // 忽略主动取消的网络请求错误
        if (error.contains("Operation canceled")) {
            return;
        }
        m_waitingRefresh = false; // 继续下一个账号
        addLogMessage("网络错误: " + error);

        // 使用与 continueAfterParse 相同的逻辑
        continueAfterParse();
    } catch (const std::exception& e) {
        addLogMessage(QString("⚠️ 网络错误处理异常: %1").arg(e.what()));
        m_waitingRefresh = false;
    } catch (...) {
        addLogMessage("⚠️ 网络错误处理发生未知异常");
        m_waitingRefresh = false;
    }
}

void MainWindow::onRefreshTimer()
{
    qDebug() << "🔍 [DEBUG] onRefreshTimer 开始 - currentIndex:" << m_currentAccountIndex;

    try {
        // ========== 基本检查 ==========
        if (!m_isRefreshing || m_accounts.isEmpty()) {
            qDebug() << "🔍 [DEBUG] 基本检查失败 - isRefreshing:" << m_isRefreshing << "accounts.size():" << m_accounts.size();
            return;
        }
        if (m_waitingRefresh) {
            qDebug() << "🔍 [DEBUG] 正在等待响应，跳过";
            // 正在等待响应，不发送新请求
            return;
        }

        // 🔒 防止在continueAfterParse处理期间触发
        if (m_continueAfterParseProcessing) {
            qDebug() << "🔍 [DEBUG] continueAfterParse正在处理，跳过定时器";
            return;
        }

    // ========== 处理主账号 (索引 -1) ==========
    if (m_currentAccountIndex == -1) {
        refreshMainAccount();
        m_currentAccountIndex = 0;  // 立即开始处理子账号，不等待
        return;
    }

    // ========== 处理子账号 ==========
    // 跳过未登录的账号，只刷新已登录的账号
    while (m_currentAccountIndex < m_accounts.size()) {
        const AccountInfo &account = m_accounts[m_currentAccountIndex];
        if (account.isLoggedIn && account.api) {
            // 找到已登录的账号，刷新它
            refreshSubAccount(m_currentAccountIndex);
            m_currentAccountIndex++;
            return; // 刷新一个账号后返回，等待响应
        }
        // 跳过未登录的账号
        m_currentAccountIndex++;
    }

        // ========== 开始下一轮 ==========
        qDebug() << "🔍 [DEBUG] 准备开始下一轮刷新";
        startNextRound();

    } catch (const std::exception& e) {
        addLogMessage(QString("⚠️ 刷新定时器异常: %1").arg(e.what()));
        m_waitingRefresh = false;
        m_isMainAccountRefreshing = false;
    } catch (...) {
        addLogMessage("⚠️ 刷新定时器发生未知异常");
        m_waitingRefresh = false;
        m_isMainAccountRefreshing = false;
    }
}

// ========== 刷新逻辑辅助函数 ==========

void MainWindow::refreshMainAccount()
{
    if (m_mainAccount.token.isEmpty() || m_mainAccount.userId.isEmpty()) {
        return; // 主账号未登录，跳过
    }

    m_isMainAccountRefreshing = true;
    m_mainAcctWaiting = true;
    m_waitingRefresh = true;
    m_currentRefreshingLabel = "主账号";

    // 设置参数
    m_api->setPageSize(m_pageSizeSpin->value());
    QString gameId = m_gameCombo->currentData().toString();
    int focusFlag = (m_focusOnlyCheck && m_focusOnlyCheck->isChecked()) ? 1 : -1;
    QString priceStr;
    if (!buildPriceStr(priceStr)) return;

    // 发送刷新请求
    m_api->mainAccountRefreshOrders(gameId, m_mainAccount.token, m_mainAccount.userId, priceStr, focusFlag);
}

bool MainWindow::hasLoggedInSubAccounts()
{
    for (const auto &acc : m_accounts) {
        if (acc.isLoggedIn && acc.api) {
            return true;
        }
    }
    return false;
}

void MainWindow::refreshSubAccount(int index)
{
    qDebug() << "🔍 [DEBUG] refreshSubAccount 调用 - index:" << index;

    if (index >= m_accounts.size()) {
        qDebug() << "🔍 [DEBUG] refreshSubAccount 索引无效，返回";
        return;
    }

    const AccountInfo &account = m_accounts[index];
    if (!account.isLoggedIn || !account.api) {
        qDebug() << "🔍 [DEBUG] refreshSubAccount 账号未登录或API为空，返回";
        // 账号未登录，跳过
        return;
    }

    qDebug() << "🔍 [DEBUG] refreshSubAccount 开始刷新账号:" << account.username;

    m_waitingRefresh = true;
    m_currentRefreshingLabel = account.username;

    // 设置参数
    account.api->setPageSize(m_pageSizeSpin->value());
    QString gameId = m_gameCombo->currentData().toString();
    int focusFlag = (m_focusOnlyCheck && m_focusOnlyCheck->isChecked()) ? 1 : -1;
    QString priceStr;
    if (!buildPriceStr(priceStr)) return;

    // 发送刷新请求
    account.api->refreshOrders(gameId, account.token, account.userId,
                               account.proxyHost, account.proxyPort,
                               account.proxyType, account.proxyUser, account.proxyPass,
                               priceStr, focusFlag);
}

void MainWindow::startNextRound()
{
    qDebug() << "🔍 [DEBUG] startNextRound 开始新一轮刷新";
    m_currentAccountIndex = -1; // 重新从主账号开始

    int interval = m_intervalSpin ? m_intervalSpin->value() : 1000; // 🚀 优化：默认1000ms
    if (m_intervalCheck && m_intervalCheck->isChecked()) {
        // 有间隔，启动定时器
        m_refreshTimer->start(interval);
    } else {
        // 无间隔，立即继续
        QMetaObject::invokeMethod(this, "onRefreshTimer", Qt::QueuedConnection);
    }
}

void MainWindow::showRefreshAccountList()
{
    QStringList currentAccounts;
    if (!m_mainAccount.token.isEmpty()) {
        currentAccounts.append("主账号");
    }
    for (const auto& acc : m_accounts) {
        if (acc.isLoggedIn) {
            currentAccounts.append(acc.username);
        }
    }

    // 开始刷新已登录账号
}

bool MainWindow::buildPriceStr(QString& priceStr)
{
    if (!m_priceFilterCheck->isChecked()) {
        priceStr.clear();
        return true; // no filter -> ok
    }
    QString minStr = m_priceMinEdit->text().trimmed();
    QString maxStr = m_priceMaxEdit->text().trimmed();

    if (minStr.isEmpty() || maxStr.isEmpty()) {
        addLogMessage("价格过滤启用但未同时输入最低价和最高价，已取消刷新");
        return false;
    }
    bool ok1, ok2;
    double minVal = minStr.toDouble(&ok1);
    double maxVal = maxStr.toDouble(&ok2);
    if (!ok1 || !ok2) {
        addLogMessage("价格过滤输入非法数字，已取消刷新");
        return false;
    }
    if (minVal > maxVal) {
        addLogMessage("最低价不能大于最高价，已取消刷新");
        return false;
    }
    priceStr = QString("%1_%2").arg(minStr).arg(maxStr);
    return true;
}

void MainWindow::loadAccountsFromFile()
{
    QFile file("代理ip.txt");
    if (!file.open(QIODevice::ReadOnly | QIODevice::Text)) {
        QMessageBox::warning(this, "错误", "无法打开代理ip.txt文件");
        return;
    }

    // 检测文件编码
    QByteArray fileData = file.readAll();
    file.seek(0);  // 重置文件指针

    // 文件大小和内容检查

    // 检查BOM
    // BOM检测
    if (fileData.startsWith("\xEF\xBB\xBF")) {
        // UTF-8 BOM
    } else if (fileData.startsWith("\xFF\xFE")) {
        // UTF-16 LE BOM
    } else if (fileData.startsWith("\xFE\xFF")) {
        // UTF-16 BE BOM
    } else {
        // 无BOM，假设为UTF-8
    }
    
    QMutexLocker locker(&m_dataMutex);
    m_accounts.clear();
    
    QTextStream in(&file);
    in.setEncoding(QStringConverter::Utf8);  // 强制使用UTF-8编码

    // 开始读取账号文件，使用UTF-8编码

    int importCount = 0;
    while (!in.atEnd()) {
        QString line = in.readLine().trimmed();


        // 处理非空行
        if (line.isEmpty()) continue;

        QString cleaned = line;
        cleaned.replace("---", ",");
        cleaned.replace(" ", ",");
        QStringList parts = cleaned.split(",", Qt::SkipEmptyParts);
        if (parts.size() < 3) continue;

        AccountInfo account;
        account.username = parts[0].trimmed();
        account.password = parts[1].trimmed();
        account.isLoggedIn = false;

        // 解析账号信息

        QString proxyField = parts[2].trimmed();
        // 支持不同分隔符 ip/port/user/pass  ip---port---user---pass  ip port user pass
        proxyField.replace("---", "/");
        proxyField.replace(" ", "/");
        if (proxyField.contains('/')) {
            // socks5: ip/port/user/pass
            QStringList p = proxyField.split('/');
            if (p.size() >= 2) {
                account.proxyHost = p[0];
                account.proxyPort = p[1].toInt();
            }
            if (p.size() >= 4) {
                account.proxyUser = p[2];
                account.proxyPass = p[3];
            }
            account.proxyType = "socks5";
        } else if (parts.size() >= 6) {
            // 6字段: ip,port,proxyUser,proxyPass
            account.proxyHost = parts[2].trimmed();
            account.proxyPort = parts[3].trimmed().toInt();
            account.proxyUser = parts[4].trimmed();
            account.proxyPass = parts[5].trimmed();
            account.proxyType = "socks5";
        } else {
            // http 4字段
            account.proxyHost = parts[2].trimmed();
            if (parts.size() >= 4) account.proxyPort = parts[3].trimmed().toInt();
            account.proxyType = "http";
        }

        // 去重
        bool exists = false;
        for (const auto &a : m_accounts) {
            if (a.username == account.username && a.proxyHost == account.proxyHost && a.proxyPort == account.proxyPort) {
                exists = true;
                break;
            }
        }
        if (!exists) {
            m_accounts.append(account);
            importCount++;
        }
    }
    
    file.close();
    updateAccountTable();
    addLogMessage("加载了 " + QString::number(m_accounts.size()) + " 个账号");

    saveSettings(); // 保存最新账号列表
}

void MainWindow::addLogMessage(const QString &message)
{
    QString timestamp = QDateTime::currentDateTime().toString("hh:mm:ss.zzz"); // 毫秒显示
    QString logMessage = QString("[%1] %2").arg(timestamp, message);
    m_logText->appendPlainText(logMessage);

    // 自动滚动：仅当滚动条已在底部时才滚到底
    auto *bar = m_logText->verticalScrollBar();
    if (bar->value() >= bar->maximum() - 2) {
        QTextCursor cursor = m_logText->textCursor();
        cursor.movePosition(QTextCursor::End);
        m_logText->setTextCursor(cursor);
    }
}

void MainWindow::updateAccountTable()
{
    m_accountTable->setRowCount(m_accounts.size());
    
    for (int i = 0; i < m_accounts.size(); ++i) {
        const AccountInfo &account = m_accounts[i];
        
        QStringList accountData;
        accountData << QString::number(i + 1)
                   << account.username
                   << account.password
                   << (account.proxyHost + ":" + QString::number(account.proxyPort))
                   << account.token
                   << (account.isLoggedIn ? "已登录" : "未登录");
        addTableRow(m_accountTable, i, accountData);
        // Set tooltips to show full content when truncated (addTableRow already sets basic tooltips)
        if (auto item = m_accountTable->item(i, 1)) item->setToolTip(account.username);
        if (auto item = m_accountTable->item(i, 2)) item->setToolTip(account.password);
        if (auto item = m_accountTable->item(i, 3)) item->setToolTip(account.proxyHost + ":" + QString::number(account.proxyPort));
        if (auto item = m_accountTable->item(i, 4)) item->setToolTip(account.token);
        if (auto item = m_accountTable->item(i, 5)) item->setToolTip(item->text()); // 状态
    }

    // 调整列宽以完整显示 Token 等较长内容
    m_accountTable->resizeColumnsToContents();
}

void MainWindow::updateOrderTable()
{
    // 批量更新表格，禁用重绘提高效率
    m_orderTable->setUpdatesEnabled(false);
    m_orderTable->setRowCount(m_orders.size());
    
    for (int i = 0; i < m_orders.size(); ++i) {
        // 最新订单排在前面：从尾部往前取
        const OrderInfo &order = m_orders[m_orders.size() - 1 - i];
        
        QStringList orderData;
        orderData << QString::number(i + 1)
                 << order.orderId
                 << order.title
                 << order.price
                 << order.timeLimit
                 << order.createUser
                 << order.zoneServer
                 << order.timestamp;
        addTableRow(m_orderTable, i, orderData);
    }

    m_orderTable->setUpdatesEnabled(true);
}

void MainWindow::startRefreshTimer()
{
    int interval = m_intervalSpin ? m_intervalSpin->value() : 0;
    if (!m_intervalCheck || m_intervalCheck->isChecked()) {
        m_refreshTimer->start(interval);
    } else {
        // 高速模式：无间隔，立即执行下一轮
        QMetaObject::invokeMethod(this, "onRefreshTimer", Qt::QueuedConnection);
    }
}

void MainWindow::stopRefreshTimer()
{
    m_refreshTimer->stop();
    if (m_mainAcctTimer) m_mainAcctTimer->stop();
}

void MainWindow::dragEnterEvent(QDragEnterEvent *event) {
    if (event->mimeData()->hasUrls()) {
        event->acceptProposedAction();
    }
}

void MainWindow::dropEvent(QDropEvent *event) {
    QList<QUrl> urls = event->mimeData()->urls();
    if (urls.isEmpty()) return;
    QString fileName = urls.first().toLocalFile();
    if (!fileName.isEmpty()) {
        importAccountsFromFile(fileName);
    }
}

void MainWindow::importAccountsFromFile(const QString &fileName) {
    QFile file(fileName);
    if (!file.open(QIODevice::ReadOnly | QIODevice::Text)) {
        addLogMessage("无法打开文件: " + fileName);
        return;
    }
    
    QMutexLocker locker(&m_dataMutex);
    QTextStream in(&file);
    in.setEncoding(QStringConverter::Utf8);

    int importCount = 0;
    while (!in.atEnd()) {
        QString line = in.readLine().trimmed();
        if (line.isEmpty()) continue;
        
        AccountInfo account;
        if (parseAccountLine(line, account)) {
            if (!isDuplicateAccount(account)) {
                m_accounts.append(account);
                importCount++;
            }
        }
    }
    
    file.close();
    updateAccountTable();
    addLogMessage(QString("拖拽导入账号 %1 个").arg(importCount));
    saveSettings();
}

bool MainWindow::parseAccountLine(const QString &line, AccountInfo &account) {
    QString cleaned = line;
    cleaned.replace("---", ",");
    cleaned.replace(" ", ",");
    QStringList parts = cleaned.split(",", Qt::SkipEmptyParts);
    
    if (parts.size() < 3) return false;

    account.username = parts[0].trimmed();
    account.password = parts[1].trimmed();
    account.isLoggedIn = false;
    
    return parseProxyInfo(parts, account);
}

bool MainWindow::parseProxyInfo(const QStringList &parts, AccountInfo &account) {
    QString proxyField = parts[2].trimmed();
    // 支持不同分隔符 ip/port/user/pass  ip---port---user---pass  ip port user pass
    proxyField.replace("---", "/");
    proxyField.replace(" ", "/");
    
    if (proxyField.contains('/')) {
        return parseSlashSeparatedProxy(proxyField, account);
    } else if (parts.size() >= 6) {
        return parseCommaSeparatedProxy(parts, account);
    } else {
        return parseHttpProxy(parts, account);
    }
}

bool MainWindow::parseSlashSeparatedProxy(const QString &proxyField, AccountInfo &account) {
    QStringList p = proxyField.split('/');
    if (p.size() < 2) return false;
    
    account.proxyHost = p[0];
    account.proxyPort = p[1].toInt();
    
    if (p.size() >= 4) {
        account.proxyUser = p[2];
        account.proxyPass = p[3];
    }
    account.proxyType = "socks5";
    return true;
}

bool MainWindow::parseCommaSeparatedProxy(const QStringList &parts, AccountInfo &account) {
    // 6字段: username,password,ip,port,proxyUser,proxyPass
    account.proxyHost = parts[2].trimmed();
    account.proxyPort = parts[3].trimmed().toInt();
    account.proxyUser = parts[4].trimmed();
    account.proxyPass = parts[5].trimmed();
    account.proxyType = "socks5";
    return true;
}

bool MainWindow::parseHttpProxy(const QStringList &parts, AccountInfo &account) {
    // http 4字段
    account.proxyHost = parts[2].trimmed();
    if (parts.size() >= 4) {
        account.proxyPort = parts[3].trimmed().toInt();
    }
    account.proxyType = "http";
    return true;
}

bool MainWindow::isDuplicateAccount(const AccountInfo &account) {
    for (const auto &existing : m_accounts) {
        if (existing.username == account.username && 
            existing.proxyHost == account.proxyHost && 
            existing.proxyPort == account.proxyPort) {
            return true;
        }
    }
    return false;
}

void MainWindow::onMainAccountRefreshClicked()
{
    // 移除按钮点击日志
    if (m_mainAccount.token.isEmpty() || m_mainAccount.userId.isEmpty()) {
        QMessageBox::warning(this, "主账号未登录", "请先登录主账号再刷新订单！");
        return;
    }
    // 同步PageSize到API（之前只在批量刷新里设置，这里补上）
    m_api->setPageSize(m_pageSizeSpin->value());
    // 设置标志，表示正在刷新主账号订单
    m_isMainAccountRefreshing = true;
    m_mainAcctWaiting = true;
    
    QString gameId = m_gameCombo->currentData().toString();
    if (gameId.isEmpty()) gameId = "107";
    
    // 移除冗余日志（刷新主账号订单详细信息）
    int focusFlag = (m_focusOnlyCheck && m_focusOnlyCheck->isChecked()) ? 1 : -1;
    QString priceStr;
    if (!buildPriceStr(priceStr)) return;
    m_api->mainAccountRefreshOrders(gameId, m_mainAccount.token, m_mainAccount.userId, priceStr, focusFlag);
}

void MainWindow::onUserInfoResult(bool success, const QString &message, const QJsonObject &info)
{
    Q_UNUSED(message);
    double sumBal = info.value("SumBal").toDouble();
    double freezeBal = info.value("FreezeBal").toDouble();
    if(sumBal==0 && !success){
        addLogMessage("获取余额失败");
        return;
    }
    double avail = sumBal - freezeBal;
    m_mainAccountBalance = avail;
    addLogMessage(QString("可用余额: %1 元").arg(avail, 0, 'f', 2));
}

// 右键弹出菜单
void MainWindow::onOrderTableContextMenu(const QPoint &pos)
{
    QTableWidget *table = qobject_cast<QTableWidget*>(sender());
    if (!table) return;

    QMenu menu(this);
    addClearMenuActions(menu, table);
    
    int row = table->rowAt(pos.y());
    QString pubId, serialNo;
    extractOrderInfo(table, row, pubId, serialNo);
    
    if (!serialNo.isEmpty()) {
        addOrderSpecificActions(menu, pubId, serialNo);
    }
    
    // 批量操作菜单
    QSet<QString> selectedPubIds = getSelectedPublisherIds(table);
    if (!selectedPubIds.isEmpty()) {
        addBatchActions(menu, selectedPubIds);
    }

    menu.exec(table->viewport()->mapToGlobal(pos));
}

void MainWindow::addClearMenuActions(QMenu &menu, QTableWidget *table) {
    if (table == m_orderTable) {
        QAction *clearAct = menu.addAction("清空新订单");
        connect(clearAct, &QAction::triggered, this, &MainWindow::onClearNewOrders);
    } else if (table == m_resultTable) {
        QAction *clearResAct = menu.addAction("清空抢单结果");
        connect(clearResAct, &QAction::triggered, this, [this]() {
            if (m_resultTable) m_resultTable->setRowCount(0);
            addLogMessage("已清空抢单结果");
        });
    }
}

void MainWindow::extractOrderInfo(QTableWidget *table, int row, QString &pubId, QString &serialNo) {
    if (row < 0) return;
    
    if (table == m_orderTable) {
        int idx = m_orders.size() - 1 - row; // 反向
        if (idx >= 0 && idx < m_orders.size()) {
            const OrderInfo &order = m_orders[idx];
            pubId = order.publishUserId;
            serialNo = order.orderId;
        }
    } else if (table == m_resultTable) {
        // 列1存订单ID, 列8隐藏存发单人UID
        if (table->item(row, RES_ORDERID)) serialNo = table->item(row, RES_ORDERID)->text();
        if (table->item(row, RES_UID)) pubId = table->item(row, RES_UID)->text();
    }
}

void MainWindow::addOrderSpecificActions(QMenu &menu, const QString &pubId, const QString &serialNo) {
    if (pubId.isEmpty()) {
        QAction *infoAct = menu.addAction("无法关注：缺少发单人ID");
        infoAct->setEnabled(false);
        return;
    }
    
    QAction *focusAct = menu.addAction("关注发单用户（所有账号）");
    QAction *unfocusAct = menu.addAction("取消关注发单用户（所有账号）");
    QAction *blackAct = menu.addAction("拉黑发单用户（主账号）");
    QAction *unblackAct = menu.addAction("取消拉黑发单用户（主账号）");

    connect(focusAct, &QAction::triggered, this, [this, pubId, serialNo]() {
        performFocusUser(pubId, serialNo);
    });

    connect(unfocusAct, &QAction::triggered, this, [this, pubId]() {
        performUnfocusUser(pubId);
    });

    connect(blackAct, &QAction::triggered, this, [this, pubId]() {
        performBlackUser(pubId);
    });

    connect(unblackAct, &QAction::triggered, this, [this, pubId]() {
        performUnblackUser(pubId);
    });
}

QSet<QString> MainWindow::getSelectedPublisherIds(QTableWidget *table) {
    QSet<QString> selectedPubIds;
    QSet<int> selRowNumbers;
    
    for (const QModelIndex &idx : table->selectionModel()->selectedIndexes()) {
        selRowNumbers.insert(idx.row());
    }
    
    for (int r : selRowNumbers) {
        QString pid;
        if (table == m_orderTable) {
            int idx = m_orders.size() - 1 - r;
            if (idx >= 0 && idx < m_orders.size()) {
                pid = m_orders[idx].publishUserId;
            }
        } else if (table == m_resultTable) {
            if (table->item(r, RES_UID)) pid = table->item(r, RES_UID)->text();
        }
        if (!pid.isEmpty()) selectedPubIds.insert(pid);
    }
    
    return selectedPubIds;
}

void MainWindow::addBatchActions(QMenu &menu, const QSet<QString> &selectedPubIds) {
    QAction *batchBlack = menu.addAction(QString("批量拉黑选中发布者(%1)").arg(selectedPubIds.size()));
    connect(batchBlack, &QAction::triggered, this, [this, selectedPubIds]() {
        performBatchBlackUsers(selectedPubIds);
    });

    QAction *batchUnblack = menu.addAction(QString("批量取消拉黑选中发布者(%1)").arg(selectedPubIds.size()));
    connect(batchUnblack, &QAction::triggered, this, [this, selectedPubIds]() {
        performBatchUnblackUsers(selectedPubIds);
    });
}

void MainWindow::performFocusUser(const QString &pubId, const QString &serialNo) {
    addLogMessage(QString("开始关注发单人 %1 所有账号(主账号TLS伪装)...").arg(pubId));
    if (!m_mainAccount.token.isEmpty() && !m_mainAccount.userId.isEmpty()) {
        m_api->mainAccountFocusUser(pubId, serialNo, m_mainAccount.userId);
    }
    for (const auto &acc : m_accounts) {
        if (acc.isLoggedIn && acc.api) {
            acc.api->focusUser(pubId, serialNo, acc.userId,
                               acc.proxyHost, acc.proxyPort, acc.proxyType,
                               acc.proxyUser, acc.proxyPass);
        }
    }
}

void MainWindow::performUnfocusUser(const QString &pubId) {
    addLogMessage(QString("开始取消关注发单人 %1 所有账号(主账号TLS伪装)...").arg(pubId));
    if (!m_mainAccount.token.isEmpty() && !m_mainAccount.userId.isEmpty()) {
        m_api->mainAccountCancelFocusUser(pubId, m_mainAccount.userId);
    }
    for (const auto &acc : m_accounts) {
        if (acc.isLoggedIn && acc.api) {
            acc.api->cancelFocusUser(pubId, acc.userId,
                                     acc.proxyHost, acc.proxyPort, acc.proxyType,
                                     acc.proxyUser, acc.proxyPass);
        }
    }
}

void MainWindow::performBlackUser(const QString &pubId) {
    addLogMessage(QString("开始拉黑发单人 %1 (主账号TLS伪装)...").arg(pubId));
    if (!m_mainAccount.token.isEmpty() && !m_mainAccount.userId.isEmpty()) {
        m_api->mainAccountAddBlackUser(pubId, m_mainAccount.userId, m_mainAccount.token);
    } else {
        addLogMessage("主账号未登录，无法拉黑");
    }
}

void MainWindow::performUnblackUser(const QString &pubId) {
    addLogMessage(QString("开始取消拉黑发单人 %1 (主账号TLS伪装)...").arg(pubId));
    if (!m_mainAccount.token.isEmpty() && !m_mainAccount.userId.isEmpty()) {
        m_api->mainAccountRemoveBlackUser(pubId, m_mainAccount.userId, m_mainAccount.token);
    } else {
        addLogMessage("主账号未登录，无法取消拉黑");
    }
}

void MainWindow::performBatchBlackUsers(const QSet<QString> &selectedPubIds) {
    if (m_mainAccount.token.isEmpty() || m_mainAccount.userId.isEmpty()) {
        addLogMessage("主账号未登录，无法批量拉黑");
        return;
    }
    int total = 0;
    for (const QString &pid : selectedPubIds) {
        // 主账号(TLS伪装)
        m_api->mainAccountAddBlackUser(pid, m_mainAccount.userId, m_mainAccount.token);
        // 所有已登录子账号并发拉黑
        for (const auto &acc : m_accounts) {
            if (acc.isLoggedIn && acc.api) {
                acc.api->addBlackUser(pid, acc.userId, acc.token,
                                      acc.proxyHost, acc.proxyPort,
                                      acc.proxyType, acc.proxyUser, acc.proxyPass);
            }
        }
        ++total;
    }
    addLogMessage(QString("已批量拉黑 %1 个发布者 (主账号+子账号同步)").arg(total));
}

void MainWindow::performBatchUnblackUsers(const QSet<QString> &selectedPubIds) {
    if (m_mainAccount.token.isEmpty() || m_mainAccount.userId.isEmpty()) {
        addLogMessage("主账号未登录，无法批量取消拉黑");
        return;
    }
    int total = 0;
    for (const QString &pid : selectedPubIds) {
        // 主账号(TLS伪装)
        m_api->mainAccountRemoveBlackUser(pid, m_mainAccount.userId, m_mainAccount.token);
        // 所有已登录子账号并发取消拉黑
        for (const auto &acc : m_accounts) {
            if (acc.isLoggedIn && acc.api) {
                acc.api->removeBlackUser(pid, acc.userId, acc.token,
                                         acc.proxyHost, acc.proxyPort,
                                         acc.proxyType, acc.proxyUser, acc.proxyPass);
            }
        }
        ++total;
    }
    addLogMessage(QString("已批量取消拉黑 %1 个发布者 (主账号+子账号同步)").arg(total));
}

void MainWindow::onClearNewOrders()
{
    {
        QMutexLocker locker(&m_dataMutex);
        m_orders.clear();
        m_seenOrderIds.clear();
        m_lastPrice.clear(); // 新增：清空价格记录
    }
    updateOrderTable();
    addLogMessage("已清空新订单存放处并重置记录");
}

// ===== 新增：订单过滤逻辑 =====
bool MainWindow::orderMatchesFilter(const QJsonObject &orderObj)
{
    QString title = orderObj.value("Title").toString();
    double price = orderObj.value("Price").toDouble();

    // 动态优化关键词结构（如果未优化或关键词已变化）
    if (!m_filterOptimized) {
        optimizeKeywordFilters();
    }
    
    // 选择当前模式的关键词集合
    const QSet<QString> &singleCharExclude = m_advancedModeCheck->isChecked() ? 
                                          m_singleCharExcludeAdvanced : m_singleCharExcludeNormal;
    const QSet<QString> &multiCharExclude = m_advancedModeCheck->isChecked() ? 
                                         m_multiCharExcludeAdvanced : m_multiCharExcludeNormal;
    
    // 1. 字符频率预检测
    bool hasCommonChar = false;
    for (const auto &ch : title) {
        if (m_commonChars.contains(ch)) {
            hasCommonChar = true;
            break;
        }
    }
    
    if (hasCommonChar) {
        // 2. 单字排除关键词快速检查（高效）
        for (int i = 0; i < title.length(); ++i) {
            QString singleChar = title.mid(i, 1);
            if (singleCharExclude.contains(singleChar)) {
                return false; // 命中排除关键词，立即拒绝
            }
        }
        
        // 3. 多字排除关键词检查
        for (const auto &kw : multiCharExclude) {
            if (title.contains(kw, Qt::CaseInsensitive)) {
                return false; // 命中排除关键词，拒绝
            }
        }
    }
    
    // 4. 根据模式进行匹配
    if(m_advancedModeCheck->isChecked()){
        // 使用优化的分组方式匹配高级模式
        if (!m_groupNames.isEmpty()) {
            // 快速检查：标题是否包含"-"符号
            bool titleHasMinus = title.contains('-');
            
            // 跟踪是否匹配
            bool matched = false;
            double matchedPrice = 0.0; // 第一个匹配的价格
            
            // 字符串小写转换(只做一次)
            QString titleLower = title.toLower();
            
            // 检查每个分组
            for (const auto &groupName : m_groupNames) {
                // 如果组中有"-"开头关键词，但标题不含"-"，跳过整组
                if (m_groupHasMinus[groupName] && !titleHasMinus) {
                    continue;
                }
                
                // 检查组内所有关键词
                const auto &entries = m_keywordGroups[groupName];
                for (const auto &entry : entries) {
                    if (titleLower.contains(entry.keyword.toLower())) {
                        if (!matched) {
                            // 只使用第一个匹配的关键词的价格
                            matchedPrice = entry.minPrice;
                            matched = true;
                        }
                        
                        // 对所有匹配的关键词都进行额外排除检查
                    }
                }
                
                if (matched) {
                    break; // 找到匹配项后停止搜索
                }
            }
            
            if (!matched) return false;  // 没有任何关键词命中
            if (price < matchedPrice) return false; // 价格不满足要求
            
            // 始终进行额外排除检查
            return performExtraExclusionCheck(title);
        } else {
            // 回退到原始匹配方式（如果分组未创建）
            bool matched = false;
            double matchedPrice = 0.0;
            for(int row=0; row<m_advancedTable->rowCount(); ++row){
                QTableWidgetItem *kwItem = m_advancedTable->item(row,0);
                if(!kwItem) continue;
                QString kw = kwItem->text().trimmed();
                if(kw.isEmpty()) continue;

                if(title.contains(kw, Qt::CaseInsensitive)) {
                    matched = true;
                    // 读取该行最低价
                    if(QTableWidgetItem *priceItem = m_advancedTable->item(row,1)){
                        bool ok=false;
                        matchedPrice = priceItem->text().toDouble(&ok);
                        if(!ok) matchedPrice = 0.0;
                    }
                    break; // 找到第一个匹配项后停止
                }
            }

            if(!matched) return false;          // 没有任何行命中关键词
            if(price < matchedPrice) return false; // 价格不满足要求
            
            // 始终进行额外排除检查
            return performExtraExclusionCheck(title);
        }
    } else {
        QStringList includeList = m_filterKeywordEdit->text().split(QRegularExpression("\\s+"), Qt::SkipEmptyParts);
        if(includeList.isEmpty()) return false; // 无关键词则不抢
        for(const QString &kw : includeList){
            if(!kw.isEmpty() && title.contains(kw, Qt::CaseInsensitive)){
                return true;
            }
        }
        return false;
    }
}

void MainWindow::loadSettings() {
    m_loadingSettings = true;
    QSettings s("OrderManager", "OrderManagerApp");
    m_usernameEdit->setText(s.value("username").toString());
    m_passwordEdit->setText(s.value("password").toString());
    m_gameCombo->setCurrentIndex(s.value("gameIndex",0).toInt());
    m_pageSizeSpin->setValue(s.value("pageSize",20).toInt());
    m_priceFilterCheck->setChecked(s.value("priceFilter",false).toBool());
    m_priceMinEdit->setText(s.value("priceMin").toString());
    m_priceMaxEdit->setText(s.value("priceMax").toString());
    m_focusOnlyCheck->setChecked(s.value("focusOnly",false).toBool());
    m_advancedModeCheck->setChecked(s.value("advancedMode",false).toBool());
    m_filterKeywordEdit->setText(s.value("filterKeyword").toString());
    m_normalExcludeKeywords = s.value("excludeKeyword").toString();
    m_advancedExcludeKeywords = s.value("advExcludeKeyword").toString();
    m_excludeKeywordEdit->setText(m_advancedModeCheck->isChecked()?m_advancedExcludeKeywords:m_normalExcludeKeywords);
    m_payPasswordEdit->setText(s.value("payPassword").toString());
    if (m_intervalCheck) m_intervalCheck->setChecked(s.value("enableInterval", true).toBool());
    if (m_intervalSpin)  m_intervalSpin->setValue(s.value("intervalMs", 3000).toInt());
    if (m_mainAcctIntervalSpin) m_mainAcctIntervalSpin->setValue(s.value("mainAcctIntervalMs", 5000).toInt());

    // 加载指纹选择设置
    if (m_fingerprintCombo) {
        int fingerprintIndex = s.value("mainAccountFingerprint", 0).toInt();
        m_fingerprintCombo->setCurrentIndex(fingerprintIndex);
        if (m_api) {
            OrderAPI::MainAccountFingerprint fingerprint =
                static_cast<OrderAPI::MainAccountFingerprint>(m_fingerprintCombo->itemData(fingerprintIndex).toInt());
            m_api->setMainAccountFingerprint(fingerprint);
        }
    }

    // 网络引擎选择设置已删除，默认使用UltraFastTLS
    if (m_api) {
        m_api->setNetworkEngine(NetworkEngine::ULTRA_FAST_TLS);
    }
    if (m_acceptTargetSpin) m_acceptTargetSpin->setValue(s.value("acceptTarget", 5).toInt());

    // 加载额外排除词格式
    m_exclusionMapFormat = s.value("exclusionMapFormat", "周胜/暗 到 超 影 排 次 一").toString();
    updateExclusionMap(m_exclusionMapFormat);

    // Advanced table
    if (m_advancedTable) {
        int rowCount = s.value("advRowCount",6).toInt();
        m_advancedTable->setRowCount(rowCount);
        for(int r=0;r<rowCount;++r){
            QString kw = s.value(QString("adv/%1/kw").arg(r)).toString();
            QString min = s.value(QString("adv/%1/min").arg(r)).toString();
            
            // 创建新单元格时设置背景色为默认（透明）
            if(!m_advancedTable->item(r,0)) {
                m_advancedTable->setItem(r, 0, createCenteredTableItem());
            }
            if(!m_advancedTable->item(r,1)) {
                m_advancedTable->setItem(r, 1, createCenteredTableItem());
            }
            
            m_advancedTable->item(r,0)->setText(kw);
            m_advancedTable->item(r,0)->setTextAlignment(Qt::AlignCenter);
            m_advancedTable->item(r,1)->setText(min);
            m_advancedTable->item(r,1)->setTextAlignment(Qt::AlignCenter);
        }
        // 更新表格颜色
        updateAdvancedTableColors();
    }

    // ========== 读取账号列表 ==========
    {
        QMutexLocker locker(&m_dataMutex);
        m_accounts.clear();
        int N = s.beginReadArray("accounts");
        for (int i = 0; i < N; ++i) {
            s.setArrayIndex(i);
            AccountInfo acc;
            acc.username   = s.value("username").toString();
            acc.password   = s.value("password").toString();
            acc.proxyHost  = s.value("proxyHost").toString();
            acc.proxyPort  = s.value("proxyPort").toInt();
            acc.proxyType  = s.value("proxyType", "http").toString();
            acc.proxyUser  = s.value("proxyUser").toString();
            acc.proxyPass  = s.value("proxyPass").toString();
            // token / 状态留空，待登录后填充
            m_accounts.append(acc);
        }
        s.endArray();
    }
    updateAccountTable();

    m_loadingSettings = false;

    // in loadSettings after reading acceptTargetSpin
    if (m_stopAfterTargetCheck) m_stopAfterTargetCheck->setChecked(s.value("stopAfterTargetEnable", true).toBool());
    if (m_acceptTargetSpin) m_acceptTargetSpin->setEnabled(m_stopAfterTargetCheck ? m_stopAfterTargetCheck->isChecked() : true);
    
    // 更新高级表格行颜色
    updateAdvancedTableColors();
}

void MainWindow::saveSettings() {
    if (m_loadingSettings) return;
    QSettings s("OrderManager", "OrderManagerApp");
    s.setValue("username", m_usernameEdit->text());
    s.setValue("password", m_passwordEdit->text());
    s.setValue("gameIndex", m_gameCombo->currentIndex());
    s.setValue("pageSize", m_pageSizeSpin->value());
    s.setValue("priceFilter", m_priceFilterCheck->isChecked());
    s.setValue("priceMin", m_priceMinEdit->text());
    s.setValue("priceMax", m_priceMaxEdit->text());
    s.setValue("focusOnly", m_focusOnlyCheck->isChecked());
    s.setValue("advancedMode", m_advancedModeCheck->isChecked());
    s.setValue("filterKeyword", m_filterKeywordEdit->text());
    // 保存排除关键词两套值
    if (m_advancedModeCheck && m_advancedModeCheck->isChecked()) {
        m_advancedExcludeKeywords = m_excludeKeywordEdit->text();
    } else {
        m_normalExcludeKeywords = m_excludeKeywordEdit->text();
    }
    s.setValue("excludeKeyword", m_normalExcludeKeywords);
    s.setValue("advExcludeKeyword", m_advancedExcludeKeywords);
    
    // 保存额外排除词格式
    if (m_exclusionMapFormat.isEmpty()) {
        m_exclusionMapFormat = "周胜/暗 到 超 影 排 次 一";
    }
    s.setValue("exclusionMapFormat", m_exclusionMapFormat);
    
    // 仅在非锁定状态才标记需要重新优化关键词过滤
    if (!m_filterLocked) {
        m_filterOptimized = false;
    }
    s.setValue("payPassword", m_payPasswordEdit->text());
    if (m_intervalCheck) s.setValue("enableInterval", m_intervalCheck->isChecked());
    if (m_intervalSpin)  s.setValue("intervalMs", m_intervalSpin->value());
    if (m_mainAcctIntervalSpin) s.setValue("mainAcctIntervalMs", m_mainAcctIntervalSpin->value());

    // 保存指纹选择设置
    if (m_fingerprintCombo) {
        s.setValue("mainAccountFingerprint", m_fingerprintCombo->currentIndex());
    }


    if (m_acceptTargetSpin) s.setValue("acceptTarget", m_acceptTargetSpin->value());
    if (m_stopAfterTargetCheck) s.setValue("stopAfterTargetEnable", m_stopAfterTargetCheck->isChecked());

    if (m_advancedTable) {
        // 只保存非空行
        QList<QPair<QString, QString>> nonEmptyRows;
        int rowCount = m_advancedTable->rowCount();
        
        for(int r=0; r<rowCount; ++r){
            QString kw = m_advancedTable->item(r,0) ? m_advancedTable->item(r,0)->text().trimmed() : "";
            QString min = m_advancedTable->item(r,1) ? m_advancedTable->item(r,1)->text().trimmed() : "";
            
            // 只保存至少关键词非空的行
            if (!kw.isEmpty()) {
                nonEmptyRows.append(qMakePair(kw, min));
            }
        }
        
        // 保存实际的非空行数量
        s.setValue("advRowCount", nonEmptyRows.size());
        
        // 保存非空行数据
        for(int r=0; r<nonEmptyRows.size(); ++r){
            s.setValue(QString("adv/%1/kw").arg(r), nonEmptyRows[r].first);
            s.setValue(QString("adv/%1/min").arg(r), nonEmptyRows[r].second);
        }
    }

    // ========== 保存账号列表（不含Token/状态） ==========
    s.beginWriteArray("accounts");
    for (int i = 0; i < m_accounts.size(); ++i) {
        s.setArrayIndex(i);
        const AccountInfo &acc = m_accounts[i];
        s.setValue("username", acc.username);
        s.setValue("password", acc.password);
        s.setValue("proxyHost", acc.proxyHost);
        s.setValue("proxyPort", acc.proxyPort);
        s.setValue("proxyType", acc.proxyType);
        s.setValue("proxyUser", acc.proxyUser);
        s.setValue("proxyPass", acc.proxyPass);
    }
    s.endArray();

    s.sync();
}

// ===== 自动接单实现 =====
void MainWindow::attemptAcceptOrder(const QJsonObject &orderObj, bool manual)
{
    QString oid = orderObj.value("SerialNo").toString();
    // 自动模式：若该订单已记录为失败(≥3次或永久错误)则跳过；
    // 手动模式(manual==true)则允许继续尝试。
    if (!manual && m_failedOrderIds.contains(oid)) {
        return;
    }

    AcceptEntry entry; 
    entry.orderObj = orderObj; 
    entry.attempts = 0; 
    entry.manual = manual;
    entry.concurrentRequests = 0;
    entry.initialBurst = true;
    entry.supplementalSent = false;
    
    // 去重：如果同一订单已在队列或正在进行，则忽略
    for (const auto &e : std::as_const(m_acceptQueue)) {
        if (e.orderObj.value("SerialNo").toString() == oid) {
            return;
        }
    }
    m_acceptQueue.append(entry);

    // 如果当前没有进行中的请求，立即发送
    sendNextAccept();
}

// ===== 接单结果回调 =====
void MainWindow::onOrderAcceptResult(bool success, const QString &message)
{
    if (m_acceptQueue.isEmpty()) {
        m_acceptInProgress = false;
        return;
    }
    AcceptEntry &entry = m_acceptQueue.first();
    QString orderId = entry.orderObj.value("SerialNo").toString();

        // 减少并发请求计数
    entry.concurrentRequests--;

    if (success) {
        addLogMessage(QString("接单成功: %1").arg(orderId));

        // 写入结果表格
        if (m_resultTable) {
            int row = 0;
            m_resultTable->insertRow(row);
            auto addCell=[this,row](int col,const QString &text){
                QTableWidgetItem *item=new QTableWidgetItem(text); item->setTextAlignment(Qt::AlignCenter); m_resultTable->setItem(row,col,item); item->setToolTip(text);
            };
            addCell(0, QString::number(row+1));
            addCell(1, orderId);
            addCell(2, entry.orderObj.value("Title").toString());
            addCell(3, QString::number(entry.orderObj.value("Price").toDouble()));
            addCell(4, entry.orderObj.value("TimeLimit").toVariant().toString());
            addCell(5, entry.orderObj.value("Create").toString());
            addCell(6, entry.orderObj.value("Zone").toString());
            addCell(7, QDateTime::currentDateTime().toString("hh:mm:ss.zzz"));
            addCell(8, "成功");
            addCell(9, entry.orderObj.value("UserID").toVariant().toString());
            for(int c=0;c<m_resultTable->columnCount();++c){ if(m_resultTable->item(row,c)) m_resultTable->item(row,c)->setBackground(Qt::green);}            
        }

        // ===== 自动停止判断 =====
        ++m_acceptSuccessCount;
        int target = m_acceptTargetSpin ? m_acceptTargetSpin->value() : 0;
        bool stopEnabled = !m_stopAfterTargetCheck || m_stopAfterTargetCheck->isChecked();
        if (stopEnabled && target > 0 && m_acceptSuccessCount >= target) {
            onStopClicked();
            addLogMessage(QString("已成功接单 %1 单，自动停止刷新").arg(m_acceptSuccessCount));
        }

        // 成功后清空队列，不管还有没有并发请求
        m_acceptQueue.clear();
        m_acceptInProgress = false;
        
        // 继续处理下一个订单
        sendNextAccept();
    } else {
        // 失败处理 - 记录失败次数，但不立即增加attempts，等所有并发请求结束后才增加
        if (entry.concurrentRequests <= 0) {
            // 所有并发请求都已完成，现在增加尝试次数
            entry.attempts++;
            
            addLogMessage(QString("接单失败(%1): %2 - %3").arg(entry.attempts).arg(orderId).arg(message));

        // 结果表格记录失败
        if (m_resultTable) {
            int row = 0;
            m_resultTable->insertRow(row);
            auto addCell=[this,row](int col,const QString &text){
                QTableWidgetItem *item=new QTableWidgetItem(text); item->setTextAlignment(Qt::AlignCenter); m_resultTable->setItem(row,col,item); item->setToolTip(text);
            };
            addCell(0, QString::number(row+1));
            addCell(1, orderId);
            addCell(2, entry.orderObj.value("Title").toString());
            addCell(3, QString::number(entry.orderObj.value("Price").toDouble()));
            addCell(4, entry.orderObj.value("TimeLimit").toVariant().toString());
            addCell(5, entry.orderObj.value("Create").toString());
            addCell(6, entry.orderObj.value("Zone").toString());
            addCell(7, QDateTime::currentDateTime().toString("hh:mm:ss.zzz"));
            addCell(8, message);
            addCell(9, entry.orderObj.value("UserID").toVariant().toString());
            for(int c=0;c<m_resultTable->columnCount();++c){ if(m_resultTable->item(row,c)) m_resultTable->item(row,c)->setBackground(Qt::red);}            
        }

            // 判断错误类型，若订单已无效则跳过，否则根据新逻辑处理
        bool permanentErr = message.contains("不存在") || message.contains("已被") || message.contains("抢完") || message.contains("拉黑");

        if (permanentErr || entry.attempts >= 3) {
            // 标记为已失败，丢弃此订单
            m_failedOrderIds.insert(orderId);
            m_acceptQueue.pop_front();
                m_acceptInProgress = false;
                
                // 继续处理下一个订单
                sendNextAccept();
            } else if (!entry.supplementalSent) {
                // 当所有并发请求都失败后，才发送补充请求
                m_acceptInProgress = false;
                
                // 直接在这里发送补充请求，而不是调用sendNextAccept()
                // 使用已声明的orderId变量，不再重复声明
                QString stamp = entry.orderObj.value("Stamp").toString();
                if (stamp.isEmpty()) stamp = "0";
                QString payPass = m_payPasswordEdit ? m_payPasswordEdit->text() : "";
                QString uidForSalt = m_mainAccount.uid.isEmpty() ? m_mainAccount.userId : m_mainAccount.uid;
                
                // 发送补充请求(TLS伪装)
                m_api->mainAccountAcceptOrder(orderId, stamp,
                               m_mainAccount.token,
                               m_mainAccount.userId,
                               payPass,
                               uidForSalt);
                
                entry.supplementalSent = true;
                entry.concurrentRequests = 1;
                m_acceptInProgress = true;
                
                QString title = entry.orderObj.value("Title").toString();
                QString priceStr;
                if (entry.orderObj.contains("Price")) {
                    if (entry.orderObj.value("Price").isDouble()) priceStr = QString::number(entry.orderObj.value("Price").toDouble());
                    else priceStr = entry.orderObj.value("Price").toString();
                }
                QString prefix = entry.manual ? "手动抢单" : "自动抢单";
                QString desc = title.isEmpty() ? orderId : QString("%1 %2").arg(title).arg(priceStr);
                addLogMessage(QString("%1: %2 (补充请求)").arg(prefix).arg(desc));
        } else {
                // 补充请求也失败，直接结束尝试，不再进行第三次尝试
                // 标记为已失败，丢弃此订单
                m_failedOrderIds.insert(orderId);
            m_acceptQueue.pop_front();
        m_acceptInProgress = false;

                // 继续处理下一个订单
    sendNextAccept();
            }
        } else {
            // 还有未完成的并发请求，等待它们完成
            // 不记录日志，避免多次重复
        }
    }
}

void MainWindow::sendNextAccept()
{
    if (m_acceptQueue.isEmpty()) return;

    AcceptEntry &entry = m_acceptQueue.first();
    const QJsonObject &orderObj = entry.orderObj;
    
    QString orderId = orderObj.value("SerialNo").toString();

    // 确保主账号已登录且有支付密码
    if (m_mainAccount.token.isEmpty() || m_mainAccount.userId.isEmpty()) {
        addLogMessage("主账号未登录，队列清空");
        m_acceptQueue.clear();
        return;
    }
    QString payPass = m_payPasswordEdit ? m_payPasswordEdit->text() : "";
    if (payPass.isEmpty()) {
        addLogMessage("支付密码为空，无法自动接单");
        m_acceptQueue.clear();
        return;
    }

    // 使用已声明的orderId变量
    QString stamp   = orderObj.value("Stamp").toString();
    if (stamp.isEmpty()) stamp = "0";

    QString proxyHost = m_mainAccount.proxyHost;
    // int proxyPort     = m_mainAccount.proxyPort;  // 未使用，已注释

    QString uidForSalt = m_mainAccount.uid.isEmpty() ? m_mainAccount.userId : m_mainAccount.uid;

    // 处理并发请求逻辑
    if (entry.initialBurst) {
        // 首次请求，发送3个并发请求
        entry.concurrentRequests = 3; // 预先设置计数，避免竞态条件
        m_acceptInProgress = true;
        
        QString title = orderObj.value("Title").toString();
        QString priceStr;
        if (orderObj.contains("Price")) {
            if (orderObj.value("Price").isDouble()) priceStr = QString::number(orderObj.value("Price").toDouble());
            else priceStr = orderObj.value("Price").toString();
        }
        QString prefix = entry.manual ? "手动抢单" : "自动抢单";
        QString desc = title.isEmpty() ? orderId : QString("%1 %2").arg(title).arg(priceStr);
        addLogMessage(QString("%1: %2 (并发3次请求)").arg(prefix).arg(desc));
        
        // 完全同时发送3个请求(TLS伪装)
    m_api->mainAccountAcceptOrder(orderId, stamp,
                       m_mainAccount.token,
                       m_mainAccount.userId,
                       payPass,
                       uidForSalt);

        m_api->mainAccountAcceptOrder(orderId, stamp,
                       m_mainAccount.token,
                       m_mainAccount.userId,
                       payPass,
                       uidForSalt);

        m_api->mainAccountAcceptOrder(orderId, stamp,
                       m_mainAccount.token,
                       m_mainAccount.userId,
                       payPass,
                       uidForSalt);
        
        entry.initialBurst = false;
    } else if (!m_acceptInProgress) {
        // 正常单次请求（轮询模式，TLS伪装）
        m_api->mainAccountAcceptOrder(orderId, stamp,
                       m_mainAccount.token,
                       m_mainAccount.userId,
                       payPass,
                       uidForSalt);
        
        entry.concurrentRequests = 1;
    m_acceptInProgress = true;
        
    QString title = orderObj.value("Title").toString();
    QString priceStr;
    if (orderObj.contains("Price")) {
        if (orderObj.value("Price").isDouble()) priceStr = QString::number(orderObj.value("Price").toDouble());
        else priceStr = orderObj.value("Price").toString();
    }
    QString prefix = entry.manual ? "手动抢单" : "自动抢单";
    QString desc = title.isEmpty() ? orderId : QString("%1 %2").arg(title).arg(priceStr);
    addLogMessage(QString("%1: %2 (第%3次)").arg(prefix).arg(desc).arg(entry.attempts+1));
    }
}

void MainWindow::startBatchRefreshLoop()
{
    // ========== 停止主账号定时器，避免冲突 ==========
    m_mainAcctTimerWasActive = m_mainAcctTimer && m_mainAcctTimer->isActive();
    if (m_mainAcctTimerWasActive) {
        m_mainAcctTimer->stop();
    }

    // ========== 基本检查 ==========
    if (m_accounts.isEmpty()) {
        QMessageBox::warning(this, "警告", "请先加载账号");
        return;
    }

    // ========== 显示当前刷新名单 ==========
    showRefreshAccountList();

    // ========== 初始化刷新状态 ==========
    m_isRefreshing = true;
    m_acceptSuccessCount = 0;
    m_currentAccountIndex = -1;  // 从主账号开始
    m_waitingRefresh = false;

    // ========== 启动刷新 ==========
    startRefreshTimer();
    QMetaObject::invokeMethod(this, "onRefreshTimer", Qt::QueuedConnection);
    addLogMessage("开始批量刷新订单");
}

void MainWindow::batchLoginNext()
{
    // 已弃用（保留空实现以兼容旧的头文件声明）
}

// ===== 新增：增量插入订单到表格 =====
void MainWindow::insertOrdersToTable(const QVector<OrderInfo>& orders)
{
    if (!m_orderTable || orders.isEmpty()) return;

    m_orderTable->setUpdatesEnabled(false);
    for (const OrderInfo &order : orders) {
        int row = 0; // 插到表头
        m_orderTable->insertRow(row);
        auto addCell=[this,row](int col,const QString &text){
            QTableWidgetItem *item=new QTableWidgetItem(text);
            item->setTextAlignment(Qt::AlignCenter);
            m_orderTable->setItem(row,col,item);
            item->setToolTip(text);
        };
        addCell(0, QString::number(m_orders.size())); // 序号暂用总大小
        addCell(1, order.orderId);
        addCell(2, order.title);
        addCell(3, order.price);
        addCell(4, order.timeLimit);
        addCell(5, order.createUser);
        addCell(6, order.zoneServer);
        addCell(7, order.timestamp);
    }
    m_orderTable->setUpdatesEnabled(true);
}

// ===== 新增：后台解析完成槽 =====
void MainWindow::onAccountParseFinished()
{
    qDebug() << "🔍 [DEBUG] onAccountParseFinished 开始";

    try {
        // 解析完成的 watcher
        auto *baseWatcher = qobject_cast<QFutureWatcherBase*>(sender());
        auto *w = static_cast<QFutureWatcher<RefreshParseResult>*>(baseWatcher);

        qDebug() << "🔍 [DEBUG] watcher检查 - baseWatcher:" << (void*)baseWatcher << "w:" << (void*)w;

        if (!w) {
            qDebug() << "🔍 [DEBUG] watcher为空，返回";
            addLogMessage("⚠️ 解析完成回调发送者为空");
            return;
        }

    qDebug() << "🔍 [DEBUG] 开始获取解析结果";
    RefreshParseResult res = w->result();
    QString key = w->property("acctKey").toString();

    qDebug() << "🔍 [DEBUG] 解析结果获取完成 - key:" << key << "newCount:" << res.newCount;

    {
        QMutexLocker locker(&m_dataMutex);
        // 更新全局订单缓存
        for (const OrderInfo &o : res.newOrders) m_orders.append(o);
        m_seenOrderIds.unite(res.newSeenIds);
        // 更新价格缓存
        for (auto it = res.newPriceMap.begin(); it != res.newPriceMap.end(); ++it)
            m_lastPrice[it.key()] = it.value();
    }

    if (res.newCount > 0) {
        insertOrdersToTable(res.newOrders);
        addLogMessage(QString("获取%1条新订单").arg(res.newCount));
    }

    // 自动接单
    for (const QJsonObject &obj : res.autoAcceptObjs) {
        if (orderMatchesFilter(obj)) {
            attemptAcceptOrder(obj, false);
        }
    }

    // 若有待处理的接单且当前未在进行中，立即触发
    sendNextAccept();

    // 若该账号还有排队包，继续解析
    if (!key.isEmpty() && !m_pendingMap[key].isEmpty()) {
        QJsonArray next = m_pendingMap[key].dequeue();
        bool firstBatch = false; // 后续包不再视为首批
        QSet<QString> seenCopy;
        QHash<QString,double> priceCopy;
        {
            QMutexLocker lk(&m_dataMutex);
            seenCopy = m_seenOrderIds;
            priceCopy = m_lastPrice;
        }
        auto fut = QtConcurrent::run([next, firstBatch, seenCopy, priceCopy, key]() {
            return parseOrders(next, firstBatch, key, seenCopy, priceCopy);
        });
        w->setFuture(fut);
        return; // 等待下一包解析完成
    }

        // 继续刷新循环（发下一个账号或重启定时器）
        qDebug() << "🔍 [DEBUG] 准备调用continueAfterParse";
        continueAfterParse();

    } catch (const std::exception& e) {
        addLogMessage(QString("⚠️ 解析完成处理异常: %1").arg(e.what()));
        m_waitingRefresh = false;
        continueAfterParse();
    } catch (...) {
        addLogMessage("⚠️ 解析完成处理发生未知异常");
        m_waitingRefresh = false;
        continueAfterParse();
    }
}

void MainWindow::continueAfterParse()
{
    qDebug() << "🔍 [DEBUG] continueAfterParse 开始";

    try {
        // 🔒 防止重复调用
        if (m_continueAfterParseProcessing) {
            qDebug() << "🔍 [DEBUG] continueAfterParse 正在处理中，跳过重复调用";
            return;
        }
        m_continueAfterParseProcessing = true;

        // ========== 重置状态标志 ==========
        m_waitingRefresh = false;
        m_isMainAccountRefreshing = false;

        qDebug() << "🔍 [DEBUG] 状态标志重置完成";

    // ========== 检查是否还有账号需要刷新 ==========
    qDebug() << "🔍 [DEBUG] 开始检查账号 - currentIndex:" << m_currentAccountIndex << "accounts.size():" << m_accounts.size();

    bool hasMoreAccounts = false;

    // 检查是否还有未刷新的已登录子账号
    for (int i = m_currentAccountIndex; i < m_accounts.size(); i++) {
        qDebug() << "🔍 [DEBUG] 检查账号" << i << "- isLoggedIn:" << m_accounts[i].isLoggedIn << "hasApi:" << (m_accounts[i].api != nullptr);
        if (m_accounts[i].isLoggedIn && m_accounts[i].api) {
            hasMoreAccounts = true;
            qDebug() << "🔍 [DEBUG] 找到需要刷新的账号:" << i;
            break;
        }
    }

    qDebug() << "🔍 [DEBUG] 账号检查完成 - hasMoreAccounts:" << hasMoreAccounts;

    if (hasMoreAccounts) {
        // 还有账号需要刷新，立即继续下一个账号（不等待间隔）
        qDebug() << "🔍 [DEBUG] 立即继续下一个账号";
        QMetaObject::invokeMethod(this, "onRefreshTimer", Qt::QueuedConnection);
    } else {
        // 等待间隔后开始下一轮
        qDebug() << "🔍 [DEBUG] 所有账号完成，准备下一轮";
        int interval = m_intervalSpin ? m_intervalSpin->value() : 1000; // 🚀 优化：默认1000ms
        qDebug() << "🔍 [DEBUG] 间隔设置:" << interval << "ms";

        if (m_intervalCheck && m_intervalCheck->isChecked() && interval > 0) {
            // 有间隔设置，使用定时器延迟执行
            qDebug() << "🔍 [DEBUG] 使用间隔定时器";
            QTimer::singleShot(interval, this, [this]() {
                qDebug() << "🔍 [DEBUG] 间隔定时器触发";
                QMetaObject::invokeMethod(this, "onRefreshTimer", Qt::QueuedConnection);
            });
            // 间隔控制等待
        } else {
            // 无间隔或间隔为0，立即继续
            qDebug() << "🔍 [DEBUG] 立即开始下一轮";
            QMetaObject::invokeMethod(this, "onRefreshTimer", Qt::QueuedConnection);
        }
    }

    // 🔒 重置保护标志（无论哪个分支都要重置）
    qDebug() << "🔍 [DEBUG] 重置continueAfterParse保护标志";
    m_continueAfterParseProcessing = false;

    } catch (const std::exception& e) {
        addLogMessage(QString("⚠️ 继续刷新处理异常: %1").arg(e.what()));
        m_waitingRefresh = false;
        m_isMainAccountRefreshing = false;
        m_continueAfterParseProcessing = false;  // 🔒 异常时也要重置
    } catch (...) {
        addLogMessage("⚠️ 继续刷新处理发生未知异常");
        m_waitingRefresh = false;
        m_isMainAccountRefreshing = false;
        m_continueAfterParseProcessing = false;  // 🔒 异常时也要重置
    }
}

// 关键词过滤优化函数
void MainWindow::optimizeKeywordFilters()
{
    // 清空现有集合
    m_commonChars.clear();
    m_singleCharExcludeNormal.clear();
    m_multiCharExcludeNormal.clear();
    m_singleCharExcludeAdvanced.clear();
    m_multiCharExcludeAdvanced.clear();
    
    // 普通模式排除关键词
    QStringList normalExcludeList = m_normalExcludeKeywords.split(QRegularExpression("\\s+"), Qt::SkipEmptyParts);
    for(const QString &kw : normalExcludeList) {
        if(kw.isEmpty()) continue;
        
        if(kw.length() == 1) {
            m_singleCharExcludeNormal.insert(kw);
            m_commonChars.insert(kw[0]);
        } else {
            m_multiCharExcludeNormal.insert(kw);
            for(const QChar &ch : kw)
                m_commonChars.insert(ch);
        }
    }
    
    // 高级模式排除关键词
    QStringList advancedExcludeList = m_advancedExcludeKeywords.split(QRegularExpression("\\s+"), Qt::SkipEmptyParts);
    for(const QString &kw : advancedExcludeList) {
        if(kw.isEmpty()) continue;
        
        if(kw.length() == 1) {
            m_singleCharExcludeAdvanced.insert(kw);
            m_commonChars.insert(kw[0]);
        } else {
            m_multiCharExcludeAdvanced.insert(kw);
            for(const QChar &ch : kw)
                m_commonChars.insert(ch);
        }
    }
    
    // 确保特殊符号在常见字符集中
    m_commonChars.insert('-');
    m_commonChars.insert('/');
    
    // 为高级模式的关键词创建智能分组
    if (m_advancedTable && m_advancedModeCheck && m_advancedModeCheck->isChecked()) {
        autoGroupKeywords();
    }
    
    m_filterOptimized = true;
}

// 自动分组关键词函数
void MainWindow::autoGroupKeywords() 
{
    // 1. 收集所有关键词和价格
    QVector<QString> allKeywords;
    QVector<double> allPrices;
    for (int row = 0; row < m_advancedTable->rowCount(); ++row) {
        QTableWidgetItem *kwItem = m_advancedTable->item(row, 0);
        QTableWidgetItem *priceItem = m_advancedTable->item(row, 1);
        if (!kwItem || kwItem->text().trimmed().isEmpty()) continue;
        
        allKeywords.append(kwItem->text().trimmed());
        
        double minPrice = 0.0;
        if (priceItem) {
            bool ok = false;
            minPrice = priceItem->text().toDouble(&ok);
            if (!ok) minPrice = 0.0;
        }
        allPrices.append(minPrice);
    }
    
    // 2. 清空现有分组
    m_keywordGroups.clear();
    m_groupNames.clear();
    m_groupHasMinus.clear();
    
    // 3. 未分组的关键词索引
    QSet<int> ungrouped;
    for (int i = 0; i < allKeywords.size(); i++) {
        ungrouped.insert(i);
    }
    
    // 4. 分组编号
    int groupNum = 1;
    
    // 5. 特殊处理 - 先检查"-"符号
    QString minusGroup = QString("group%1_minus").arg(groupNum++);
    QSet<int> toRemove;
    
    for (int idx : ungrouped) {
        if (allKeywords[idx].contains('-')) {
            m_keywordGroups[minusGroup].append({allKeywords[idx], allPrices[idx]});
            toRemove.insert(idx);
        }
    }
    
    if (!toRemove.isEmpty()) {
        m_groupNames.append(minusGroup);
        m_groupHasMinus[minusGroup] = true;
        
        // 从未分组集合中移除已分组的关键词
        for (int idx : toRemove) {
            ungrouped.remove(idx);
        }
    } else {
        // 没有包含"-"的关键词，不创建这个组
        groupNum--;
    }
    
    // 6. 逐次提取特征字符并分组
    const int MAX_GROUPS = 8; // 限制最大组数
    
    while (!ungrouped.isEmpty() && groupNum <= MAX_GROUPS) {
        // 统计剩余关键词中各字符出现次数
        QHash<QChar, int> charCount;
        for (int idx : ungrouped) {
            const QString &keyword = allKeywords[idx];
            QSet<QChar> uniqueChars; // 每个关键词中的字符只计算一次
            
            for (const QChar &ch : keyword) {
                uniqueChars.insert(ch);
            }
            
            for (const QChar &ch : uniqueChars) {
                charCount[ch]++;
            }
        }
        
        // 找出出现最多的字符
        QChar mostCommon;
        int maxCount = 0;
        for (auto it = charCount.begin(); it != charCount.end(); ++it) {
            if (it.value() > maxCount) {
                maxCount = it.value();
                mostCommon = it.key();
            }
        }
        
        // 如果没有找到字符(理论上不会发生)
        if (maxCount == 0) break;
        
        // 将包含该字符的关键词分到当前组
        QString groupName = QString("group%1_%2").arg(groupNum).arg(mostCommon);
        toRemove.clear();
        bool hasMinus = false;
        
        for (int idx : ungrouped) {
            if (allKeywords[idx].contains(mostCommon)) {
                m_keywordGroups[groupName].append({allKeywords[idx], allPrices[idx]});
                if (allKeywords[idx].contains('-')) hasMinus = true;
                toRemove.insert(idx);
            }
        }
        
        m_groupNames.append(groupName);
        m_groupHasMinus[groupName] = hasMinus;
        
        // 从未分组集合中移除已分组的关键词
        for (int idx : toRemove) {
            ungrouped.remove(idx);
        }
        
        // 下一个分组
        groupNum++;
    }
    
    // 7. 处理剩余未分组的关键词(如果有)
    if (!ungrouped.isEmpty()) {
        QString otherGroup = "group_other";
        bool hasMinus = false;
        
        for (int idx : ungrouped) {
            m_keywordGroups[otherGroup].append({allKeywords[idx], allPrices[idx]});
            if (allKeywords[idx].contains('-')) hasMinus = true;
        }
        
        m_groupNames.append(otherGroup);
        m_groupHasMinus[otherGroup] = hasMinus;
    }
    
    // 8. 大组二次分组处理
    const int MAX_GROUP_SIZE = 8; // 如果一个组超过8个关键词，考虑二次分组
    
    QStringList groupsToProcess = m_groupNames;
    for (const QString &groupName : groupsToProcess) {
        auto &entries = m_keywordGroups[groupName];
        
        // 如果组太大，进行二次分组
        if (entries.size() > MAX_GROUP_SIZE) {
            // 提取该组所有关键词
            QVector<QString> subKeywords;
            QVector<double> subPrices;
            for (const auto &entry : entries) {
                subKeywords.append(entry.keyword);
                subPrices.append(entry.minPrice);
            }
            
            // 清空原组
            entries.clear();
            
            // 对这组关键词进行子分组
            QHash<QString, QVector<KeywordPriceEntry>> subGroups;
            QSet<int> subUngrouped;
            for (int i = 0; i < subKeywords.size(); i++) {
                subUngrouped.insert(i);
            }
            
            int subGroupNum = 1;
            while (!subUngrouped.isEmpty() && subGroupNum <= 3) { // 最多3个子组
                // 统计字符频率
                QHash<QChar, int> charCount;
                for (int idx : subUngrouped) {
                    const QString &keyword = subKeywords[idx];
                    QSet<QChar> uniqueChars;
                    
                    for (const QChar &ch : keyword) {
                        uniqueChars.insert(ch);
                    }
                    
                    for (const QChar &ch : uniqueChars) {
                        charCount[ch]++;
                    }
                }
                
                // 找出最常见字符
                QChar mostCommon;
                int maxCount = 0;
                
                for (auto it = charCount.begin(); it != charCount.end(); ++it) {
                    if (it.value() > maxCount) {
                        maxCount = it.value();
                        mostCommon = it.key();
                    }
                }
                
                if (maxCount == 0) break;
                
                // 子分组名
                QString subGroupName = QString("%1_sub%2_%3").arg(groupName).arg(subGroupNum).arg(mostCommon);
                QSet<int> toRemove;
                bool hasMinus = false;
                
                // 分配关键词
                for (int idx : subUngrouped) {
                    if (subKeywords[idx].contains(mostCommon)) {
                        subGroups[subGroupName].append({subKeywords[idx], subPrices[idx]});
                        if (subKeywords[idx].contains('-')) hasMinus = true;
                        toRemove.insert(idx);
                    }
                }
                
                // 更新未分组集合
                for (int idx : toRemove) {
                    subUngrouped.remove(idx);
                }
                
                // 记录子组信息
                m_groupNames.append(subGroupName);
                m_groupHasMinus[subGroupName] = hasMinus;
                
                subGroupNum++;
            }
            
            // 处理剩余未子分组的关键词
            if (!subUngrouped.isEmpty()) {
                QString remainderGroup = groupName + "_remainder";
                bool hasMinus = false;
                
                for (int idx : subUngrouped) {
                    subGroups[remainderGroup].append({subKeywords[idx], subPrices[idx]});
                    if (subKeywords[idx].contains('-')) hasMinus = true;
                }
                
                m_groupNames.append(remainderGroup);
                m_groupHasMinus[remainderGroup] = hasMinus;
            }
            
            // 将子分组添加到主分组映射
            for (auto it = subGroups.begin(); it != subGroups.end(); ++it) {
                m_keywordGroups[it.key()] = it.value();
            }
            
            // 从主组名列表中移除原始大组
            m_groupNames.removeOne(groupName);
        }
    }
    
    // 9. 更新分组统计表格
    updateGroupStatsTable();

    // 在函数末尾添加以下代码，检查是否需要额外排除
    for (auto it = m_keywordGroups.begin(); it != m_keywordGroups.end(); ++it) {
        for (int i = 0; i < it.value().size(); ++i) {
            // 不再检查[+额外]标记，完全依赖手动格式设置
        }
    }
}

// 添加更新分组统计表格的函数
void MainWindow::updateGroupStatsTable()
{
    if (!m_groupStatsTable) return;
    
    // 清空现有行
    m_groupStatsTable->setRowCount(0);
    
    // 添加所有分组统计
    for (const QString &groupName : m_groupNames) {
        int row = m_groupStatsTable->rowCount();
        m_groupStatsTable->insertRow(row);
        
        // 分组名
        QString displayName = groupName;
        // 如果是自动生成的分组名，简化显示
        if (displayName.startsWith("group")) {
            // 提取特征字符
            QStringList parts = displayName.split('_');
            if (parts.size() > 1) {
                QString feature = parts.last();
                if (feature == "minus") {
                    displayName = "含'-'符号";
                } else {
                    displayName = QString("含'%1'字符").arg(feature);
                }
                
                // 如果是子组
                if (displayName.contains("sub")) {
                    displayName = QString("子组: %1").arg(feature);
                }
            }
        }
        
        QTableWidgetItem *nameItem = new QTableWidgetItem(displayName);
        nameItem->setTextAlignment(Qt::AlignCenter); // 设置文本居中
        m_groupStatsTable->setItem(row, 0, nameItem);
        
        // 关键词数量
        int count = m_keywordGroups[groupName].size();
        QTableWidgetItem *countItem = new QTableWidgetItem(QString::number(count));
        countItem->setTextAlignment(Qt::AlignCenter); // 设置文本居中
        m_groupStatsTable->setItem(row, 1, countItem);
        
        // 根据数量设置背景色
        if (count > 8) {
            nameItem->setBackground(QColor(255, 200, 200)); // 红色：组过大
            countItem->setBackground(QColor(255, 200, 200));
        } else if (count > 5) {
            nameItem->setBackground(QColor(255, 255, 200)); // 黄色：组较大
            countItem->setBackground(QColor(255, 255, 200));
        } else {
            nameItem->setBackground(QColor(200, 255, 200)); // 绿色：组大小合适
            countItem->setBackground(QColor(200, 255, 200));
        }
    }
    
    // 添加总计行
    int totalKeywords = 0;
    for (const auto &group : m_keywordGroups) {
        totalKeywords += group.size();
    }
    
    int row = m_groupStatsTable->rowCount();
    m_groupStatsTable->insertRow(row);
    
    QTableWidgetItem *totalNameItem = new QTableWidgetItem("总计");
    totalNameItem->setFont(QFont("", -1, QFont::Bold));
    totalNameItem->setTextAlignment(Qt::AlignCenter); // 设置文本居中
    m_groupStatsTable->setItem(row, 0, totalNameItem);
    
    QTableWidgetItem *totalCountItem = new QTableWidgetItem(QString::number(totalKeywords));
    totalCountItem->setTextAlignment(Qt::AlignCenter); // 设置文本居中
    totalCountItem->setFont(QFont("", -1, QFont::Bold));
    m_groupStatsTable->setItem(row, 1, totalCountItem);
}

// 设置工作线程
void MainWindow::setupWorkerThreads()
{
    // 创建常驻的额外排除关键词线程
    m_extraExclusionThread = new QThread(this);
    m_extraExclusionWorker = new FilterWorker();
    m_extraExclusionWorker->moveToThread(m_extraExclusionThread);
    
    // 连接信号和槽
    connect(this, &MainWindow::startExtraExclusionFilter,
            m_extraExclusionWorker, &FilterWorker::processExtraExclusions);
    connect(m_extraExclusionWorker, &FilterWorker::resultReady,
            this, &MainWindow::onExtraExclusionResult);
    
    // 启动线程
    m_extraExclusionThread->start();
}

// 处理额外排除关键词结果
void MainWindow::onExtraExclusionResult(bool result)
{
    m_extraExclusionResult.storeRelease(result ? 1 : 0);
    m_extraExclusionFinished.storeRelease(1);
    checkAllFiltersFinished();
}

// 检查所有过滤器是否完成
void MainWindow::checkAllFiltersFinished()
{
    if (m_extraExclusionFinished.loadAcquire() > 0) {
        emit allFiltersFinished();
    }
}

// 添加新方法，用于更新排除词映射
void MainWindow::updateExclusionMap(const QString &formatString) {
    if (m_extraExclusionWorker) {
        m_extraExclusionWorker->setExclusionMap(formatString);
    }
    
    // 更新高级表格颜色
    updateAdvancedTableColors();
}

// 添加新方法，更新高级表格中的行颜色
void MainWindow::updateAdvancedTableColors() {
    if (!m_advancedTable) return;
    
    // 解析当前的排除词格式，获取所有关键词部分
    QSet<QString> keywordsWithExclusions;
    
    // 支持多个关键词设置额外排除词的格式
    QStringList formatParts = m_exclusionMapFormat.split(';', Qt::SkipEmptyParts);
    for (const QString &formatPart : formatParts) {
        if (formatPart.contains('/')) {
            QString keyword = formatPart.section('/', 0, 0).trimmed();
            if (!keyword.isEmpty()) {
                keywordsWithExclusions.insert(keyword);
            }
        }
    }
    
    // 如果只有一个格式设置（旧格式）
    if (keywordsWithExclusions.isEmpty() && m_exclusionMapFormat.contains('/')) {
        QString keyword = m_exclusionMapFormat.section('/', 0, 0).trimmed();
        if (!keyword.isEmpty()) {
            keywordsWithExclusions.insert(keyword);
        }
    }
    
    // 遍历高级表格的所有行
    for (int row = 0; row < m_advancedTable->rowCount(); ++row) {
        QTableWidgetItem *kwItem = m_advancedTable->item(row, 0);
        if (!kwItem) continue;
        
        QString keyword = kwItem->text().trimmed();
        
        // 检查这个关键词是否是当前设置了额外排除的关键词
        bool hasExtraExclusions = (!keyword.isEmpty() && keywordsWithExclusions.contains(keyword));
        
        // 设置背景色和字体
        QColor bgColor;
        if (keyword.isEmpty()) {
            // 空行使用白色背景
            bgColor = QColor(255, 255, 255); // 白色
        } else if (hasExtraExclusions) {
            // 有排除词的关键词行
            bgColor = QColor(255, 255, 200); // 浅黄色
        } else {
            // 普通关键词行
            bgColor = QColor(200, 255, 200); // 浅绿色
        }
        
        // 应用到该行的所有单元格
        for (int col = 0; col < m_advancedTable->columnCount(); ++col) {
            QTableWidgetItem *item = m_advancedTable->item(row, col);
            if (item) {
                item->setBackground(bgColor);
                // 不设置文字颜色，保持默认黑色
            }
        }
    }
}

// 创建网络引擎切换控件
void MainWindow::createNetworkEngineControls()
{
    // 网络引擎相关控件已简化
}



void MainWindow::onMainAccountFocusResult(const QString &fid, bool ok, const QString &msg)
{
    // 优化字符串拼接，使用arg()替代+操作符
    const QString status = ok ? (msg.isEmpty() ? "成功" : msg) : QStringLiteral("失败:") + msg;
    addLogMessage(QStringLiteral("主账号 关注结果[%1]: %2").arg(fid, status));
}

void MainWindow::onMainAccountCancelFocusResult(const QString &fid, bool ok, const QString &msg)
{
    const QString status = ok ? (msg.isEmpty() ? "成功" : msg) : QStringLiteral("失败:") + msg;
    addLogMessage(QStringLiteral("主账号 取消关注结果[%1]: %2").arg(fid, status));
}

void MainWindow::onMainAccountBlackUserResult(const QString &uid, bool ok, const QString &msg)
{
    const QString status = ok ? (msg.isEmpty() ? "成功" : msg) : QStringLiteral("失败:") + msg;
    addLogMessage(QStringLiteral("主账号 拉黑结果[%1]: %2").arg(uid, status));
}

void MainWindow::onMainAccountRemoveBlackUserResult(const QString &uid, bool ok, const QString &msg)
{
    const QString status = ok ? (msg.isEmpty() ? "成功" : msg) : QStringLiteral("失败:") + msg;
    addLogMessage(QStringLiteral("主账号 取消拉黑结果[%1]: %2").arg(uid, status));
}

// 辅助函数：执行额外排除检查
bool MainWindow::performExtraExclusionCheck(const QString& title)
{
    // 重置状态
    m_extraExclusionResult.storeRelease(0);
    m_extraExclusionFinished.storeRelease(0);

    // 发送到常驻线程处理
    emit startExtraExclusionFilter(title);

    // 等待结果
    QEventLoop loop;
    connect(this, &MainWindow::allFiltersFinished, &loop, &QEventLoop::quit);

    // 设置超时，避免永久等待
    QTimer::singleShot(100, &loop, &QEventLoop::quit);
    loop.exec();

    // 返回额外排除检查结果
    return m_extraExclusionResult.loadAcquire() > 0;
}

void MainWindow::testProxyFunctionality()
{
    addLogMessage("开始测试各账号独立代理配置...");

    if (m_accounts.isEmpty()) {
        addLogMessage("没有可用的测试账号");
        return;
    }

    // 测试各账号的代理配置独立性
    int testedCount = 0;
    for (int i = 0; i < m_accounts.size() && testedCount < 5; i++) {
        const AccountInfo& account = m_accounts[i];
        if (!account.api) continue;

        QString proxyInfo = account.api->getCurrentProxyInfo();
        QString accountInfo = QString("账号[%1]: %2").arg(account.username).arg(proxyInfo);

        if (!account.proxyHost.isEmpty()) {
            addLogMessage(QString("🔗 %1 -> 代理: %2:%3")
                         .arg(accountInfo)
                         .arg(account.proxyHost)
                         .arg(account.proxyPort));
        } else {
            addLogMessage(QString("📡 %1 -> 直连模式").arg(accountInfo));
        }

        testedCount++;
    }

    if (testedCount == 0) {
        addLogMessage("没有找到已初始化的账号API实例");
    } else {
        addLogMessage(QString("已验证 %1 个账号的代理配置独立性").arg(testedCount));
    }

    // 验证UltraFastTLS实例的独立性
    QSet<quintptr> instanceAddresses;
    for (const AccountInfo& account : m_accounts) {
        if (account.api) {
            // 通过getCurrentProxyInfo获取实例地址
            QString info = account.api->getCurrentProxyInfo();
            // 提取实例地址（简化实现）
            instanceAddresses.insert(reinterpret_cast<quintptr>(account.api));
        }
    }

    addLogMessage(QString("发现 %1 个独立的OrderAPI实例").arg(instanceAddresses.size()));
    addLogMessage("各账号代理独立性测试完成");
}