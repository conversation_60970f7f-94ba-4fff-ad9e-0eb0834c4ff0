#ifndef NETWORK_ENGINE_INTERFACE_H
#define NETWORK_ENGINE_INTERFACE_H

#include <QString>
#include <QObject>
#include <QJsonObject>

/**
 * @brief 网络请求结果
 */
struct NetworkResult {
    bool success = false;
    QString data;
    QString errorMessage;
    int statusCode = 0;
    qint64 responseTime = 0;

    // 兼容性字段
    QString responseData;    // 与data相同，为了兼容性
    QString requestUrl;      // 请求URL
    QString requestData;     // 请求数据
    QString engineName;      // 引擎名称
    QDateTime timestamp;     // 时间戳

    NetworkResult() = default;
    NetworkResult(bool success, const QString& data,
                 const QString& error = "", int code = 0, qint64 time = 0)
        : success(success), data(data), errorMessage(error),
          statusCode(code), responseTime(time), responseData(data) {}
};

/**
 * @brief 网络引擎接口
 */
class INetworkEngine : public QObject {
    Q_OBJECT
    
public:
    explicit INetworkEngine(QObject* parent = nullptr) : QObject(parent) {}
    virtual ~INetworkEngine() = default;
    
    // 核心接口
    virtual bool initialize() = 0;
    virtual void cleanup() = 0;
    virtual NetworkResult executeRequest(const QString& url, 
                                       const QString& postData,
                                       const QJsonObject& headers) = 0;
    
    // 性能接口
    virtual QString getEngineName() const = 0;
    virtual qint64 getAverageResponseTime() const = 0;
    virtual int getRequestCount() const = 0;
    
    // 配置接口
    virtual void setTimeout(int timeoutMs) = 0;
    virtual void setUserAgent(const QString& userAgent) = 0;
    virtual void setProxy(const QString& host, int port, 
                         const QString& type = "http") = 0;

signals:
    void requestCompleted(const NetworkResult& result);
    void requestFailed(const QString& error);
    void debugLog(const QString& message);
};

/**
 * @brief 网络引擎工厂
 */
class NetworkEngineFactory {
public:
    enum EngineType {
        ULTRA_FAST_TLS,
        CURL,
        QT_NETWORK
    };
    
    static INetworkEngine* createEngine(EngineType type, QObject* parent = nullptr);
    static QStringList getAvailableEngines();
    static EngineType getRecommendedEngine();
};

#endif // NETWORK_ENGINE_INTERFACE_H
