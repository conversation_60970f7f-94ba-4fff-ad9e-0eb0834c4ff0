#ifndef ORDERAPI_H
#define ORDERAPI_H

#include <QObject>
#include <QNetworkAccessManager>
#include <QNetworkReply>
#include <QJsonObject>
#include <QJsonDocument>
#include <QJsonArray>
#include <QCryptographicHash>
#include <QUrlQuery>
#include <QTimer>
#include <functional>
#include <QHash>
#include <QMutex>
#include <chrono>

// 前向声明
class UltraFastTLS;
// class NetworkEngineManager;  // 暂时注释掉

// 为了避免不完整类型错误，我们需要包含UltraFastTLS头文件
#include "../network/ultrafasttls.h"

// 网络引擎类型
enum class NetworkEngine {
    ULTRA_FAST_TLS  // 使用 UltraFastTLS (唯一引擎)
};

// 性能监控统计结构
struct NetworkPerformanceStats {
    // UltraFastTLS 统计
    int ultraFastRequests = 0;
    double ultraFastTotalTime = 0.0;
    double ultraFastAverageTime = 0.0;
    int ultraFastFailures = 0;
};

// 子账号专用性能统计结构
struct SubAccountPerformanceStats {
    // 子账号登录统计
    int loginRequestsUltraFast = 0;
    int loginRequestsTraditional = 0;
    int loginSuccessUltraFast = 0;
    int loginSuccessTraditional = 0;
    double loginAverageTimeUltraFast = 0.0;
    double loginAverageTimeTraditional = 0.0;
    
    // 子账号刷新订单统计
    int refreshRequestsUltraFast = 0;
    int refreshRequestsTraditional = 0;
    int refreshSuccessUltraFast = 0;
    int refreshSuccessTraditional = 0;
    double refreshAverageTimeUltraFast = 0.0;
    double refreshAverageTimeTraditional = 0.0;
    
    // 总体性能提升
    double overallPerformanceGain = 0.0;  // UltraFastTLS相对于传统方式的性能提升
    QString recommendedEngine = "UltraFastTLS";  // 推荐的引擎
};

// 子账号UltraFastTLS配置结构
struct SubAccountUltraFastTLSConfig {
    bool enableUltraFastTLS = true;           // 是否启用UltraFastTLS
    bool autoFallback = true;                 // 是否自动回退
    UltraFastTLS::BrowserFingerprint fingerprint = UltraFastTLS::BrowserFingerprint::QUARK_BROWSER;  // 指纹类型
    int connectionPoolSize = 3;               // 连接池大小（子账号使用较小的池）
    int requestTimeout = 30000;               // 请求超时时间（毫秒）
    bool enableDebugLog = false;              // 是否启用调试日志
};

// TLS 指纹验证信息结构
struct TLSFingerprintInfo {
    QString ja3Hash;           // JA3 指纹哈希
    QString ja4Hash;           // JA4 指纹哈希
    QString userAgent;         // User-Agent
    QString tlsVersion;        // TLS 版本
    QStringList cipherSuites;  // 密码套件列表
    QStringList extensions;    // TLS 扩展列表
    bool isQuarkCompatible;    // 是否与夸克浏览器兼容
    double matchScore;         // 匹配度评分 (0-100)
};

class OrderAPI : public QObject
{
    Q_OBJECT

public:
    // 导入全局枚举到类作用域
    using NetworkEngine = ::NetworkEngine;
    explicit OrderAPI(QObject *parent = nullptr);
    ~OrderAPI();

    // 静态加密/签名工具函数
    static QString md5Encrypt(const QString& str);
    static QString signstr(const QString& action, const QString& data, const QString& token = QString());
    static QString encryptPassword(const QString& password, const QString& loginId);

    // 新增：严格还原Python协议的参数拼接和签名
    QString buildPostData(const QList<QPair<QString, QString>> &params);
    QString signstr(const QString &action, const QList<QPair<QString, QString>> &params, const QString &token);

    // 接口方法（仅C++内部用，无Q_INVOKABLE）
    void loginPreCheck(const QString& account, const QString& proxy, std::function<void(const QString&)> callback);
    void login(const QString& account, const QString& password, const QString& proxy, std::function<void(const QString&)> callback);
    
    // 订单相关
    void refreshOrders(const QString &gameId, const QString &token, const QString &userId,
                      const QString &proxyHost = "", int proxyPort = 0,
                      const QString &proxyType = "http",
                      const QString &proxyUser = "", const QString &proxyPass = "",
                      const QString &priceStr = "",
                      int focusedFlag = -1);
    void acceptOrder(const QString &orderId, const QString &stamp, const QString &token, 
                    const QString &userId, const QString &payPassword, const QString &loginId,
                    const QString &proxyHost = "", int proxyPort = 0,
                    const QString &proxyUser = "", const QString &proxyPass = "");
    
    // 用户信息
    void getUserInfo(const QString &token, const QString &userId,
                    const QString &proxyHost = "", int proxyPort = 0);

    // 主账号专用请求方法（使用TLS指纹伪装）
    void mainAccountLogin(const QString& account, const QString& password, std::function<void(const QString&)> callback);
    void mainAccountRefreshOrders(const QString &gameId, const QString &token, const QString &userId,
                                 const QString &priceStr = "", int focusedFlag = -1);
    void mainAccountRefreshOrdersAsync(const QString &gameId, const QString &token, const QString &userId,
                                      const QString &priceStr = "", int focusedFlag = -1);
    void mainAccountGetUserInfo(const QString &token, const QString &userId);
    void mainAccountAddBlackUser(const QString &blackUserId, const QString &userId, const QString &token);
    void mainAccountRemoveBlackUser(const QString &blackUserId, const QString &userId, const QString &token);
    void mainAccountFocusUser(const QString &focusUserId, const QString &serialNo, const QString &userId);
    void mainAccountCancelFocusUser(const QString &focusUserId, const QString &userId);
    void mainAccountAcceptOrder(const QString &orderId, const QString &stamp, const QString &token,
                               const QString &userId, const QString &payPassword, const QString &loginId);

    // 关注/取消关注发单人
    void focusUser(const QString &focusUserId, const QString &serialNo, const QString &userId,
                   const QString &proxyHost = "", int proxyPort = 0, const QString &proxyType = "http",
                   const QString &proxyUser = "", const QString &proxyPass = "");
    void cancelFocusUser(const QString &focusUserId, const QString &userId,
                         const QString &proxyHost = "", int proxyPort = 0, const QString &proxyType = "http",
                         const QString &proxyUser = "", const QString &proxyPass = "");

    // 拉黑 / 取消拉黑发单人
    void addBlackUser(const QString &memberUserId, const QString &userId, const QString &token,
                      const QString &proxyHost = "", int proxyPort = 0, const QString &proxyType = "http",
                      const QString &proxyUser = "", const QString &proxyPass = "");
    void removeBlackUser(const QString &memberUserId, const QString &userId, const QString &token,
                         const QString &proxyHost = "", int proxyPort = 0, const QString &proxyType = "http",
                         const QString &proxyUser = "", const QString &proxyPass = "");

    // 每次刷新拉取的最大订单数，默认20，可由外部设置
    void setPageSize(int size) { m_pageSize = size > 0 ? size : 20; }

    // 主账号指纹选择
    enum MainAccountFingerprint {
        QUARK_FINGERPRINT,
        WECHAT_FINGERPRINT
    };

    void setMainAccountFingerprint(MainAccountFingerprint fingerprint);
    MainAccountFingerprint getMainAccountFingerprint() const;

    // 子账号UltraFastTLS配置管理
    void setSubAccountUltraFastTLSConfig(const SubAccountUltraFastTLSConfig& config);
    SubAccountUltraFastTLSConfig getSubAccountUltraFastTLSConfig() const;
    void resetSubAccountPerformanceStats();
    SubAccountPerformanceStats getSubAccountPerformanceStats() const;
    QString generateSubAccountPerformanceReport() const;

    // 代理配置验证
    QString getCurrentProxyInfo() const;
    bool hasProxyConfigured() const;

signals:
    void loginResult(bool success, const QString &message, const QString &token, const QString &userId, const QString &uid);
    void orderRefreshResult(bool success, const QString &message, const QJsonArray &orders, int recordCount);
    void orderAcceptResult(bool success, const QString &message);
    void userInfoResult(bool success, const QString &message, const QJsonObject &userInfo);
    void networkError(const QString &error);
    void debugLog(const QString &msg);
    void focusUserResult(bool success, const QString &message, const QString &focusUserId);
    void cancelFocusUserResult(bool success, const QString &message, const QString &focusUserId);
    void addBlackResult(bool success, const QString &message, const QString &userId);
    void removeBlackResult(bool success, const QString &message, const QString &userId);
    // 新增：解析线程快速发现满足条件订单后立即发射（避免再经过 UI 层）
    void fastOrderFound(const QJsonObject &orderObj);

private slots:
    void onNetworkReplyFinished(); // 处理网络响应

signals:
    // 直接抢单信号（从解析线程发出，在同一线程处理）
    void takeOrder(const QString &serialNo, const QString &stamp, 
                  const QString &userId, const QString &payPass, 
                  const QString &loginId, const QString &proxyHost, 
                  int proxyPort, const QString &proxyUser, const QString &proxyPass);

public slots:
    // 处理抢单结果（在UI线程执行）
    void reportAcceptResult(bool success, const QString &message, const QString &serialNo);
    void logPerformance(const QString &message);
    
    // 直接处理抢单请求（在解析线程中执行）
    void processAcceptOrder(const QString &serialNo, const QString &stamp,
                           const QString &userId, const QString &payPass,
                           const QString &loginId, const QString &proxyHost,
                           int proxyPort, const QString &proxyUser, const QString &proxyPass);

    // 设置网络引擎
    void setNetworkEngine(NetworkEngine engine) { m_networkEngine = engine; }
    NetworkEngine getNetworkEngine() const { return m_networkEngine; }

    // 统一的网络请求接口
    QString executeNetworkRequest(const QString &url, const QString &postData, const QString &headers = QString());

    // 性能监控
    NetworkPerformanceStats getPerformanceStats() const { return m_performanceStats; }
    void resetPerformanceStats();
    QString getPerformanceReport() const;

    // TLS 指纹验证
    TLSFingerprintInfo getCurrentFingerprint() const;
    bool verifyQuarkFingerprint() const;
    QString getFingerprintReport() const;



    // 新网络引擎架构（实验性）- 暂时注释掉
    // void initializeNewNetworkEngines();
    // void cleanupNewNetworkEngines();
    // QString executeRequestWithNewEngine(const QString& url, const QString& postData = QString());

private:
    // Keep-alive timer to hold the long-lived connection
    QTimer *m_keepAliveTimer {nullptr};
    
    // 抢单通道预热定时器
    QTimer *m_acceptPreheater {nullptr};
 
    void sendKeepAlive();
    
    // 预热抢单通道
    void preheatAcceptChannel();
    
    // 判断订单是否符合抢单条件
    bool isOrderMatchingCriteria(const QJsonObject &orderObj);
 
    // 记录最近一次网络活动时间（用于跳过多余 keep-alive）
    void markActivity();

    QNetworkAccessManager *m_networkManager;
    // 抢单专用网络管理器
    QNetworkAccessManager *m_acceptManager;
    QNetworkReply *m_currentReply;
    
    // 存储当前登录信息，用于快速抢单
    QString m_currentToken;
    QString m_currentUserId;
    QString m_pendingPassword;
    QString m_pendingLoginId;
    QString m_encryptedPayPass; // 提前计算好的加密支付密码
    
    // gzip解压函数
    QByteArray gunzipData(const QByteArray &compressedData);
    
    // 加密相关
    QString md5Hash(const QString &input);
    QString passwordEncrypt(const QString &password, const QString &loginId);
    QString signEncrypt(const QString &action, const QString &data, const QString &token = "");
    
    // 网络请求
    // 已废弃：统一走 acceptOrder / 专用实现；若仍调用则编译期报错
    void sendRequest(const QString &url, const QUrlQuery &params, const QString &proxyHost = "", int proxyPort = 0) = delete;
    void setProxy(const QString &host, int port);
    
    // 时间戳
    QString getCurrentTimestamp();
    
    // 解析响应
    void parseLoginResponse(const QByteArray &data);
    void parseOrderRefreshResponse(const QByteArray &data);
    void parseOrderAcceptResponse(const QByteArray &data);
    void parseUserInfoResponse(const QByteArray &data);

    // TLS指纹伪装相关
    QString executeUltraFastTLSRequest(const QString &url, const QString &postData, const QString &headers);
    QString buildTlsFingerprint();

    // 子账号专用网络请求方法 - UltraFastTLS引擎升级
    QString executeSubAccountRequest(const QString &url, const QString &postData, const QString &headers);
    QString executeTraditionalSubAccountRequest(const QString &url, const QString &postData, const QString &headers);
    void updateSubAccountPerformanceStats(bool success, const QString &engineType);



    // 登录流程临时参数
    QString m_pendingUsername;
    QString m_pendingProxyHost;
    int m_pendingProxyPort;

    // 连接复用优化
    QString m_lastProxyConfig; // 上次的代理配置，用于判断是否需要重建连接

    // 网络引擎配置
    NetworkEngine m_networkEngine = NetworkEngine::ULTRA_FAST_TLS;  // 使用 UltraFastTLS
    class UltraFastTLS* m_ultraFastTLS = nullptr;         // 主账号UltraFastTLS 实例
    class UltraFastTLS* m_subAccountUltraFastTLS = nullptr; // 子账号独立UltraFastTLS 实例


    // 新的网络引擎架构（与现有代码并存）- 暂时注释掉
    // NetworkEngineManager* m_engineManager = nullptr;      // 网络引擎管理器

    // 性能监控
    mutable NetworkPerformanceStats m_performanceStats;
    mutable QMutex m_statsMutex;

    // 子账号专用配置和统计
    SubAccountUltraFastTLSConfig m_subAccountConfig;
    mutable SubAccountPerformanceStats m_subAccountStats;
    mutable QMutex m_subAccountStatsMutex;



    int m_pageSize = 20; // 默认一次拉20条订单

    // 主账号指纹选择
    MainAccountFingerprint m_mainAccountFingerprint = QUARK_FINGERPRINT; // 默认使用夸克指纹

    // 全局共享的网络管理器，避免重复握手
    // 代理池：key = 类型://host:port，或 direct
    static QHash<QString, QNetworkAccessManager*> s_managerPool;

    // 记录最近网络活动时间
    static qint64 s_lastDirectActivity;

    // 🚀 全局共享的UltraFastTLS实例，用于子账号连接复用
    static class UltraFastTLS* s_sharedSubAccountTLS;
    static QString s_lastSharedProxyConfig; // 共享实例的上次代理配置

    // 获取指定代理对应的 QNAM（不存在则创建）
    // 新增 user/pass 支持，便于带认证的代理
    static QNetworkAccessManager* managerForProxy(const QString &host, int port, const QString &type,
                                                 const QString &user = QString(), const QString &pass = QString());
};

#endif // ORDERAPI_H 