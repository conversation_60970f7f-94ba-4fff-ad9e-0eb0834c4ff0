{"artifacts": [{"path": "zlib/libzlib.dll"}, {"path": "zlib/libzlib.dll.a"}, {"path": "zlib/libzlib.pdb"}], "backtrace": 1, "backtraceGraph": {"commands": ["add_library", "install", "add_definitions", "include_directories", "target_include_directories"], "files": ["C:/Libraries/zlib131/zlib-1.3.1/CMakeLists.txt", "CMakeLists.txt"], "nodes": [{"file": 0}, {"command": 0, "file": 0, "line": 152, "parent": 0}, {"command": 1, "file": 0, "line": 182, "parent": 0}, {"file": 1}, {"command": 2, "file": 1, "line": 127, "parent": 3}, {"command": 2, "file": 1, "line": 105, "parent": 3}, {"command": 2, "file": 1, "line": 103, "parent": 3}, {"command": 2, "file": 0, "line": 44, "parent": 0}, {"command": 3, "file": 0, "line": 85, "parent": 0}, {"command": 4, "file": 0, "line": 153, "parent": 0}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": "-g -fdiagnostics-color=always"}], "defines": [{"backtrace": 4, "define": "NDEBUG"}, {"backtrace": 4, "define": "NOMINMAX"}, {"backtrace": 5, "define": "OPENSSL_3_PLUS"}, {"backtrace": 6, "define": "OPENSSL_FOUND"}, {"backtrace": 4, "define": "WIN32_LEAN_AND_MEAN"}, {"define": "ZLIB_DLL"}, {"backtrace": 4, "define": "_CRT_SECURE_NO_WARNINGS"}, {"backtrace": 4, "define": "_HAS_ITERATOR_DEBUGGING=0"}, {"backtrace": 7, "define": "_LARGEFILE64_SOURCE=1"}, {"backtrace": 4, "define": "_SECURE_SCL=0"}], "includes": [{"backtrace": 8, "path": "C:/eee/cc/build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/zlib"}, {"backtrace": 8, "path": "C:/eee/cc"}, {"backtrace": 9, "path": "C:/Libraries/zlib131/zlib-1.3.1"}], "language": "C", "sourceIndexes": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14]}], "id": "zlib::@036b525f80ea8433013a", "install": {"destinations": [{"backtrace": 2, "path": "C:/Program Files (x86)/OrderManager/lib"}, {"backtrace": 2, "path": "C:/Program Files (x86)/OrderManager/bin"}], "prefix": {"path": "C:/Program Files (x86)/OrderManager"}}, "link": {"commandFragments": [{"fragment": "", "role": "flags"}, {"fragment": "-lkernel32 -luser32 -lgdi32 -lwinspool -lshell32 -lole32 -loleaut32 -luuid -lcomdlg32 -ladvapi32", "role": "libraries"}], "language": "C"}, "name": "zlib", "nameOnDisk": "libzlib.dll", "paths": {"build": "zlib", "source": "C:/Libraries/zlib131/zlib-1.3.1"}, "sourceGroups": [{"name": "Source Files", "sourceIndexes": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14]}, {"name": "Object Files", "sourceIndexes": [15]}, {"name": "<PERSON><PERSON>", "sourceIndexes": [16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26]}, {"name": "CMake Rules", "sourceIndexes": [27]}], "sources": [{"backtrace": 1, "compileGroupIndex": 0, "path": "C:/Libraries/zlib131/zlib-1.3.1/adler32.c", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "C:/Libraries/zlib131/zlib-1.3.1/compress.c", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "C:/Libraries/zlib131/zlib-1.3.1/crc32.c", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "C:/Libraries/zlib131/zlib-1.3.1/deflate.c", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "C:/Libraries/zlib131/zlib-1.3.1/gzclose.c", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "C:/Libraries/zlib131/zlib-1.3.1/gzlib.c", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "C:/Libraries/zlib131/zlib-1.3.1/gzread.c", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "C:/Libraries/zlib131/zlib-1.3.1/gzwrite.c", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "C:/Libraries/zlib131/zlib-1.3.1/inflate.c", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "C:/Libraries/zlib131/zlib-1.3.1/infback.c", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "C:/Libraries/zlib131/zlib-1.3.1/inftrees.c", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "C:/Libraries/zlib131/zlib-1.3.1/inffast.c", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "C:/Libraries/zlib131/zlib-1.3.1/trees.c", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "C:/Libraries/zlib131/zlib-1.3.1/uncompr.c", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "C:/Libraries/zlib131/zlib-1.3.1/zutil.c", "sourceGroupIndex": 0}, {"backtrace": 1, "isGenerated": true, "path": "build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/zlib/zlib1rc.obj", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/zlib/zconf.h", "sourceGroupIndex": 2}, {"backtrace": 1, "path": "C:/Libraries/zlib131/zlib-1.3.1/zlib.h", "sourceGroupIndex": 2}, {"backtrace": 1, "path": "C:/Libraries/zlib131/zlib-1.3.1/crc32.h", "sourceGroupIndex": 2}, {"backtrace": 1, "path": "C:/Libraries/zlib131/zlib-1.3.1/deflate.h", "sourceGroupIndex": 2}, {"backtrace": 1, "path": "C:/Libraries/zlib131/zlib-1.3.1/gzguts.h", "sourceGroupIndex": 2}, {"backtrace": 1, "path": "C:/Libraries/zlib131/zlib-1.3.1/inffast.h", "sourceGroupIndex": 2}, {"backtrace": 1, "path": "C:/Libraries/zlib131/zlib-1.3.1/inffixed.h", "sourceGroupIndex": 2}, {"backtrace": 1, "path": "C:/Libraries/zlib131/zlib-1.3.1/inflate.h", "sourceGroupIndex": 2}, {"backtrace": 1, "path": "C:/Libraries/zlib131/zlib-1.3.1/inftrees.h", "sourceGroupIndex": 2}, {"backtrace": 1, "path": "C:/Libraries/zlib131/zlib-1.3.1/trees.h", "sourceGroupIndex": 2}, {"backtrace": 1, "path": "C:/Libraries/zlib131/zlib-1.3.1/zutil.h", "sourceGroupIndex": 2}, {"backtrace": 0, "isGenerated": true, "path": "build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/zlib/zlib1rc.obj.rule", "sourceGroupIndex": 3}], "type": "SHARED_LIBRARY"}