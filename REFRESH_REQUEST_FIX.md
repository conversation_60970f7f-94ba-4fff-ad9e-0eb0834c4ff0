# 🔧 刷新请求关键差异修复

## 🚨 **发现的关键差异**

### 1. **参数差异** ⭐ **已修复**

**主账号有额外参数**：
```cpp
// 主账号的额外参数处理
if (!priceStr.isEmpty()) {
    params.append({"PriceStr", priceStr});  // 价格筛选参数
}

if (focusedFlag != -1) {
    params.append({"IsFocused", QString::number(focusedFlag)});  // 关注标志参数
}
```

**子账号之前缺少这些参数** → **已修复**

### 2. **URL构建方式差异** ⭐ **已修复**

**主账号**：
```cpp
QString url = buildApiUrl(ApiConstants::Actions::LEVEL_ORDER_LIST);
// 结果: https://server.dailiantong.com.cn/API/AppService.ashx?Action=LevelOrderList
```

**子账号之前**：
```cpp
// 硬编码URL
request.setUrl(QUrl("https://server.dailiantong.com.cn/API/AppService.ashx?Action=LevelOrderList"));
```

**子账号现在** → **已修复**：
```cpp
QString fullUrl = buildApiUrl(ApiConstants::Actions::LEVEL_ORDER_LIST);
```

### 3. **网络引擎使用方式差异** ⭐ **已修复**

**主账号**：
```cpp
// 主账号使用统一的网络引擎接口
QString response = executeNetworkRequest(url, postData, "");
// executeNetworkRequest() 内部调用 executeUltraFastTLSRequest()
```

**子账号之前**：
```cpp
// 混合使用QNetworkRequest + UltraFastTLS（不一致）
QNetworkRequest request;
request.setHeader(...);  // 设置传统请求头
// 然后又使用 UltraFastTLS
```

**子账号现在** → **已修复**：
```cpp
// 直接使用UltraFastTLS，与主账号保持一致
response = m_subAccountUltraFastTLS->executeRequest(fullUrl, postData);
```

## 🛠️ **已应用的修复**

### 1. **统一参数处理**
```cpp
// 子账号现在包含与主账号相同的额外参数
// 添加价格筛选参数
if (!priceStr.isEmpty()) {
    params.append({"PriceStr", priceStr});
}

// 添加关注标志
if (focusedFlag != -1) {
    params.append({"IsFocused", QString::number(focusedFlag)});
}
```

### 2. **统一URL构建**
```cpp
// 子账号现在使用与主账号相同的URL构建方式
QString fullUrl = buildApiUrl(ApiConstants::Actions::LEVEL_ORDER_LIST);
```

### 3. **清理混合代码**
```cpp
// 移除了所有QNetworkRequest相关的遗留代码
// 子账号现在完全使用UltraFastTLS
```

## 📊 **修复前后对比**

### 修复前的子账号请求
```
参数: 缺少PriceStr和IsFocused参数
URL: 硬编码字符串
网络: QNetworkRequest + UltraFastTLS混合
结果: 参数不完整，可能导致服务器拒绝请求
```

### 修复后的子账号请求
```
参数: 与主账号完全一致，包含所有必要参数
URL: 使用buildApiUrl()统一构建
网络: 纯UltraFastTLS，与主账号保持一致
结果: 应该与主账号行为完全一致
```

## 🎯 **预期效果**

重新编译运行后，子账号的请求应该与主账号完全一致：

### ✅ **参数对比**
```
[主账号] 📋 关键参数: AppVer=4.6.4
[主账号] 📋 关键参数: PriceStr=0-999999
[主账号] 📋 关键参数: IsFocused=1
[主账号] 📋 关键参数: UserID=12345
[主账号] 📋 关键参数: Token=abcdef

[13064756431] 📋 关键参数: AppVer=4.6.4      ← 一致
[13064756431] 📋 关键参数: PriceStr=0-999999  ← 现在有了
[13064756431] 📋 关键参数: IsFocused=1        ← 现在有了
[13064756431] 📋 关键参数: UserID=12345       ← 一致
[13064756431] 📋 关键参数: Token=abcdef       ← 一致
```

### ✅ **网络请求成功**
```
[13064756431] [子账号TLS] ✅ SOCKS5代理连接成功: server.dailiantong.com.cn:443
[13064756431] [子账号TLS] 🔐 开始SSL握手 (超时: 20秒, 代理模式: 是)
[13064756431] [子账号TLS] ✅ SSL握手成功 (耗时: 1234ms)
[13064756431] 📥 子账号请求响应长度: 1234  ← 不再是0
```

## 🔍 **关键修复点总结**

1. **参数完整性** - 子账号现在包含所有必要的参数
2. **URL一致性** - 使用相同的URL构建方法
3. **网络引擎纯净性** - 完全使用UltraFastTLS，移除混合代码
4. **浏览器指纹一致性** - 使用相同的WECHAT_BROWSER指纹
5. **AppVer版本一致性** - 使用相同的4.6.4版本

## 🚀 **立即测试**

**请重新编译并运行程序**：

1. **批量登录账号**
2. **点击"开始刷新"**
3. **对比主账号和子账号的参数日志**
4. **观察子账号的SSL握手和响应**

现在子账号的刷新请求应该与主账号完全一致，能够正常获取订单数据！

---

**重要**：这是一个全面的刷新请求修复，解决了参数、URL、网络引擎的所有差异。现在子账号应该能正常刷新订单了！
