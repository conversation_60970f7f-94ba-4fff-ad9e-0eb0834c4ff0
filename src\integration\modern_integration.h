#pragma once

#include <QObject>
#include <memory>

class MainWindow;
class NetworkManager;
class LegacyApiAdapter;
class AppController;
class OrderService;
class UltraFastTLSAdapter;

class ModernIntegration : public QObject
{
    Q_OBJECT

public:
    explicit ModernIntegration(MainWindow* mainWindow, QObject* parent = nullptr);
    ~ModernIntegration() = default;  // 智能指针自动管理内存

    bool initialize();
    void shutdown();
    bool isInitialized() const { return m_initialized; }

    // 访问器（返回原始指针以保持兼容性）
    LegacyApiAdapter* getLegacyAdapter() const { return m_legacyApiAdapter.get(); }
    NetworkManager* getNetworkManager() const { return m_networkManager.get(); }

private:
    void setupNetworkManager();
    void setupLegacyApiAdapter();

    MainWindow* m_mainWindow = nullptr;
    bool m_initialized = false;

    // 使用智能指针管理组件
    std::unique_ptr<NetworkManager> m_networkManager;
    std::unique_ptr<LegacyApiAdapter> m_legacyApiAdapter;
    std::unique_ptr<AppController> m_appController;
    std::unique_ptr<OrderService> m_orderService;
    std::unique_ptr<UltraFastTLSAdapter> m_tlsAdapter;
};
