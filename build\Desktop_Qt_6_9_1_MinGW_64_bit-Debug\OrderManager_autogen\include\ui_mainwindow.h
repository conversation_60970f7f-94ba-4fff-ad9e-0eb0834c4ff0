/********************************************************************************
** Form generated from reading UI file 'mainwindow.ui'
**
** Created by: Qt User Interface Compiler version 6.9.1
**
** WARNING! All changes made in this file will be lost when recompiling UI file!
********************************************************************************/

#ifndef UI_MAINWINDOW_H
#define UI_MAINWINDOW_H

#include <QtCore/QVariant>
#include <QtWidgets/QApplication>
#include <QtWidgets/QCheckBox>
#include <QtWidgets/QComboBox>
#include <QtWidgets/QFrame>
#include <QtWidgets/QHeaderView>
#include <QtWidgets/QLabel>
#include <QtWidgets/QLineEdit>
#include <QtWidgets/QMainWindow>
#include <QtWidgets/QMenuBar>
#include <QtWidgets/QPushButton>
#include <QtWidgets/QSpinBox>
#include <QtWidgets/QStatusBar>
#include <QtWidgets/QTableWidget>
#include <QtWidgets/QTextEdit>
#include <QtWidgets/QWidget>

QT_BEGIN_NAMESPACE

class Ui_MainWindow
{
public:
    QWidget *centralwidget;
    QTextEdit *logText;
    QTableWidget *accountTable;
    QLineEdit *usernameEdit;
    QLineEdit *passwordEdit;
    QComboBox *gameCombo;
    QLabel *label_5;
    QPushButton *refreshButton;
    QPushButton *stopButton;
    QPushButton *loginButton;
    QLineEdit *payPasswordEdit;
    QTableWidget *advancedTable;
    QFrame *statsLine;
    QTableWidget *groupStatsTable;
    QCheckBox *priceFilterCheck;
    QCheckBox *focusOnlyCheck;
    QCheckBox *intervalCheck;
    QSpinBox *pageSizeSpin;
    QLineEdit *priceMinEdit;
    QLineEdit *filterKeywordEdit;
    QLineEdit *priceMaxEdit;
    QPushButton *batchLoginButton;
    QCheckBox *advancedModeCheck;
    QLineEdit *excludeKeywordEdit;
    QTableWidget *orderTable;
    QLabel *pageSizeLabel;
    QTableWidget *resultTable;
    QSpinBox *intervalSpin;
    QLabel *stopTargetLabel;
    QSpinBox *acceptTargetSpin;
    QCheckBox *stopAfterTargetCheck;
    QMenuBar *menubar;
    QStatusBar *statusbar;

    void setupUi(QMainWindow *MainWindow)
    {
        if (MainWindow->objectName().isEmpty())
            MainWindow->setObjectName("MainWindow");
        MainWindow->resize(1200, 700);
        centralwidget = new QWidget(MainWindow);
        centralwidget->setObjectName("centralwidget");
        logText = new QTextEdit(centralwidget);
        logText->setObjectName("logText");
        logText->setGeometry(QRect(10, 390, 421, 261));
        accountTable = new QTableWidget(centralwidget);
        if (accountTable->columnCount() < 6)
            accountTable->setColumnCount(6);
        QTableWidgetItem *__qtablewidgetitem = new QTableWidgetItem();
        accountTable->setHorizontalHeaderItem(0, __qtablewidgetitem);
        QTableWidgetItem *__qtablewidgetitem1 = new QTableWidgetItem();
        accountTable->setHorizontalHeaderItem(1, __qtablewidgetitem1);
        QTableWidgetItem *__qtablewidgetitem2 = new QTableWidgetItem();
        accountTable->setHorizontalHeaderItem(2, __qtablewidgetitem2);
        QTableWidgetItem *__qtablewidgetitem3 = new QTableWidgetItem();
        accountTable->setHorizontalHeaderItem(3, __qtablewidgetitem3);
        QTableWidgetItem *__qtablewidgetitem4 = new QTableWidgetItem();
        accountTable->setHorizontalHeaderItem(4, __qtablewidgetitem4);
        QTableWidgetItem *__qtablewidgetitem5 = new QTableWidgetItem();
        accountTable->setHorizontalHeaderItem(5, __qtablewidgetitem5);
        accountTable->setObjectName("accountTable");
        accountTable->setGeometry(QRect(440, 10, 581, 131));
        accountTable->setColumnCount(6);
        accountTable->horizontalHeader()->setStretchLastSection(true);
        usernameEdit = new QLineEdit(centralwidget);
        usernameEdit->setObjectName("usernameEdit");
        usernameEdit->setGeometry(QRect(10, 10, 167, 25));
        passwordEdit = new QLineEdit(centralwidget);
        passwordEdit->setObjectName("passwordEdit");
        passwordEdit->setGeometry(QRect(10, 40, 167, 25));
        passwordEdit->setEchoMode(QLineEdit::EchoMode::Normal);
        gameCombo = new QComboBox(centralwidget);
        gameCombo->addItem(QString());
        gameCombo->addItem(QString());
        gameCombo->addItem(QString());
        gameCombo->addItem(QString());
        gameCombo->setObjectName("gameCombo");
        gameCombo->setGeometry(QRect(50, 100, 121, 25));
        label_5 = new QLabel(centralwidget);
        label_5->setObjectName("label_5");
        label_5->setGeometry(QRect(10, 100, 34, 19));
        refreshButton = new QPushButton(centralwidget);
        refreshButton->setObjectName("refreshButton");
        refreshButton->setGeometry(QRect(190, 100, 93, 28));
        refreshButton->setMinimumSize(QSize(93, 0));
        stopButton = new QPushButton(centralwidget);
        stopButton->setObjectName("stopButton");
        stopButton->setGeometry(QRect(290, 100, 93, 28));
        stopButton->setMinimumSize(QSize(93, 0));
        loginButton = new QPushButton(centralwidget);
        loginButton->setObjectName("loginButton");
        loginButton->setGeometry(QRect(190, 40, 93, 28));
        loginButton->setMinimumSize(QSize(93, 0));
        payPasswordEdit = new QLineEdit(centralwidget);
        payPasswordEdit->setObjectName("payPasswordEdit");
        payPasswordEdit->setGeometry(QRect(10, 70, 167, 25));
        payPasswordEdit->setEchoMode(QLineEdit::EchoMode::Normal);
        advancedTable = new QTableWidget(centralwidget);
        if (advancedTable->columnCount() < 2)
            advancedTable->setColumnCount(2);
        QTableWidgetItem *__qtablewidgetitem6 = new QTableWidgetItem();
        advancedTable->setHorizontalHeaderItem(0, __qtablewidgetitem6);
        QTableWidgetItem *__qtablewidgetitem7 = new QTableWidgetItem();
        advancedTable->setHorizontalHeaderItem(1, __qtablewidgetitem7);
        advancedTable->setObjectName("advancedTable");
        advancedTable->setGeometry(QRect(10, 230, 421, 151));
        statsLine = new QFrame(centralwidget);
        statsLine->setObjectName("statsLine");
        statsLine->setGeometry(QRect(10, 385, 421, 3));
        statsLine->setFrameShape(QFrame::Shape::HLine);
        statsLine->setFrameShadow(QFrame::Shadow::Sunken);
        groupStatsTable = new QTableWidget(centralwidget);
        if (groupStatsTable->columnCount() < 2)
            groupStatsTable->setColumnCount(2);
        QTableWidgetItem *__qtablewidgetitem8 = new QTableWidgetItem();
        groupStatsTable->setHorizontalHeaderItem(0, __qtablewidgetitem8);
        QTableWidgetItem *__qtablewidgetitem9 = new QTableWidgetItem();
        groupStatsTable->setHorizontalHeaderItem(1, __qtablewidgetitem9);
        groupStatsTable->setObjectName("groupStatsTable");
        groupStatsTable->setGeometry(QRect(1030, 10, 161, 131));
        groupStatsTable->setEditTriggers(QAbstractItemView::EditTrigger::NoEditTriggers);
        groupStatsTable->setAlternatingRowColors(true);
        groupStatsTable->setSelectionMode(QAbstractItemView::SelectionMode::SingleSelection);
        groupStatsTable->setTextElideMode(Qt::TextElideMode::ElideMiddle);
        groupStatsTable->horizontalHeader()->setMinimumSectionSize(80);
        groupStatsTable->horizontalHeader()->setDefaultSectionSize(100);
        groupStatsTable->horizontalHeader()->setStretchLastSection(false);
        priceFilterCheck = new QCheckBox(centralwidget);
        priceFilterCheck->setObjectName("priceFilterCheck");
        priceFilterCheck->setGeometry(QRect(190, 10, 117, 23));
        focusOnlyCheck = new QCheckBox(centralwidget);
        focusOnlyCheck->setObjectName("focusOnlyCheck");
        focusOnlyCheck->setGeometry(QRect(290, 70, 147, 23));
        intervalCheck = new QCheckBox(centralwidget);
        intervalCheck->setObjectName("intervalCheck");
        intervalCheck->setGeometry(QRect(280, 200, 91, 23));
        intervalCheck->setChecked(true);
        pageSizeSpin = new QSpinBox(centralwidget);
        pageSizeSpin->setObjectName("pageSizeSpin");
        pageSizeSpin->setGeometry(QRect(360, 40, 49, 25));
        pageSizeSpin->setMinimum(1);
        pageSizeSpin->setMaximum(200);
        pageSizeSpin->setValue(20);
        priceMinEdit = new QLineEdit(centralwidget);
        priceMinEdit->setObjectName("priceMinEdit");
        priceMinEdit->setGeometry(QRect(250, 10, 61, 25));
        filterKeywordEdit = new QLineEdit(centralwidget);
        filterKeywordEdit->setObjectName("filterKeywordEdit");
        filterKeywordEdit->setGeometry(QRect(10, 138, 421, 25));
        priceMaxEdit = new QLineEdit(centralwidget);
        priceMaxEdit->setObjectName("priceMaxEdit");
        priceMaxEdit->setGeometry(QRect(320, 10, 61, 25));
        batchLoginButton = new QPushButton(centralwidget);
        batchLoginButton->setObjectName("batchLoginButton");
        batchLoginButton->setGeometry(QRect(190, 70, 93, 28));
        batchLoginButton->setMinimumSize(QSize(93, 0));
        advancedModeCheck = new QCheckBox(centralwidget);
        advancedModeCheck->setObjectName("advancedModeCheck");
        advancedModeCheck->setGeometry(QRect(10, 200, 117, 23));
        excludeKeywordEdit = new QLineEdit(centralwidget);
        excludeKeywordEdit->setObjectName("excludeKeywordEdit");
        excludeKeywordEdit->setGeometry(QRect(10, 170, 421, 25));
        orderTable = new QTableWidget(centralwidget);
        if (orderTable->columnCount() < 7)
            orderTable->setColumnCount(7);
        QTableWidgetItem *__qtablewidgetitem10 = new QTableWidgetItem();
        orderTable->setHorizontalHeaderItem(0, __qtablewidgetitem10);
        QTableWidgetItem *__qtablewidgetitem11 = new QTableWidgetItem();
        orderTable->setHorizontalHeaderItem(1, __qtablewidgetitem11);
        QTableWidgetItem *__qtablewidgetitem12 = new QTableWidgetItem();
        orderTable->setHorizontalHeaderItem(2, __qtablewidgetitem12);
        QTableWidgetItem *__qtablewidgetitem13 = new QTableWidgetItem();
        orderTable->setHorizontalHeaderItem(3, __qtablewidgetitem13);
        QTableWidgetItem *__qtablewidgetitem14 = new QTableWidgetItem();
        orderTable->setHorizontalHeaderItem(4, __qtablewidgetitem14);
        QTableWidgetItem *__qtablewidgetitem15 = new QTableWidgetItem();
        orderTable->setHorizontalHeaderItem(5, __qtablewidgetitem15);
        QTableWidgetItem *__qtablewidgetitem16 = new QTableWidgetItem();
        orderTable->setHorizontalHeaderItem(6, __qtablewidgetitem16);
        orderTable->setObjectName("orderTable");
        orderTable->setGeometry(QRect(440, 340, 751, 311));
        orderTable->setColumnCount(7);
        orderTable->horizontalHeader()->setStretchLastSection(true);
        pageSizeLabel = new QLabel(centralwidget);
        pageSizeLabel->setObjectName("pageSizeLabel");
        pageSizeLabel->setGeometry(QRect(290, 40, 71, 19));
        resultTable = new QTableWidget(centralwidget);
        if (resultTable->columnCount() < 7)
            resultTable->setColumnCount(7);
        QTableWidgetItem *__qtablewidgetitem17 = new QTableWidgetItem();
        resultTable->setHorizontalHeaderItem(0, __qtablewidgetitem17);
        QTableWidgetItem *__qtablewidgetitem18 = new QTableWidgetItem();
        resultTable->setHorizontalHeaderItem(1, __qtablewidgetitem18);
        QTableWidgetItem *__qtablewidgetitem19 = new QTableWidgetItem();
        resultTable->setHorizontalHeaderItem(2, __qtablewidgetitem19);
        QTableWidgetItem *__qtablewidgetitem20 = new QTableWidgetItem();
        resultTable->setHorizontalHeaderItem(3, __qtablewidgetitem20);
        QTableWidgetItem *__qtablewidgetitem21 = new QTableWidgetItem();
        resultTable->setHorizontalHeaderItem(4, __qtablewidgetitem21);
        QTableWidgetItem *__qtablewidgetitem22 = new QTableWidgetItem();
        resultTable->setHorizontalHeaderItem(5, __qtablewidgetitem22);
        QTableWidgetItem *__qtablewidgetitem23 = new QTableWidgetItem();
        resultTable->setHorizontalHeaderItem(6, __qtablewidgetitem23);
        resultTable->setObjectName("resultTable");
        resultTable->setGeometry(QRect(440, 150, 751, 181));
        resultTable->setColumnCount(7);
        resultTable->horizontalHeader()->setStretchLastSection(true);
        intervalSpin = new QSpinBox(centralwidget);
        intervalSpin->setObjectName("intervalSpin");
        intervalSpin->setGeometry(QRect(360, 200, 61, 25));
        intervalSpin->setMinimum(0);
        intervalSpin->setMaximum(60000);
        intervalSpin->setValue(3000);
        stopTargetLabel = new QLabel(centralwidget);
        stopTargetLabel->setObjectName("stopTargetLabel");
        stopTargetLabel->setGeometry(QRect(150, 200, 71, 19));
        acceptTargetSpin = new QSpinBox(centralwidget);
        acceptTargetSpin->setObjectName("acceptTargetSpin");
        acceptTargetSpin->setGeometry(QRect(210, 200, 41, 25));
        acceptTargetSpin->setMinimum(1);
        acceptTargetSpin->setMaximum(1000);
        acceptTargetSpin->setValue(5);
        stopAfterTargetCheck = new QCheckBox(centralwidget);
        stopAfterTargetCheck->setObjectName("stopAfterTargetCheck");
        stopAfterTargetCheck->setGeometry(QRect(130, 200, 21, 25));
        stopAfterTargetCheck->setChecked(true);
        MainWindow->setCentralWidget(centralwidget);
        menubar = new QMenuBar(MainWindow);
        menubar->setObjectName("menubar");
        menubar->setGeometry(QRect(0, 0, 1200, 25));
        MainWindow->setMenuBar(menubar);
        statusbar = new QStatusBar(MainWindow);
        statusbar->setObjectName("statusbar");
        MainWindow->setStatusBar(statusbar);

        retranslateUi(MainWindow);

        QMetaObject::connectSlotsByName(MainWindow);
    } // setupUi

    void retranslateUi(QMainWindow *MainWindow)
    {
        MainWindow->setWindowTitle(QCoreApplication::translate("MainWindow", "\350\256\242\345\215\225\347\256\241\347\220\206\345\231\250", nullptr));
        QTableWidgetItem *___qtablewidgetitem = accountTable->horizontalHeaderItem(0);
        ___qtablewidgetitem->setText(QCoreApplication::translate("MainWindow", "\345\272\217\345\217\267", nullptr));
        QTableWidgetItem *___qtablewidgetitem1 = accountTable->horizontalHeaderItem(1);
        ___qtablewidgetitem1->setText(QCoreApplication::translate("MainWindow", "\347\224\250\346\210\267\345\220\215", nullptr));
        QTableWidgetItem *___qtablewidgetitem2 = accountTable->horizontalHeaderItem(2);
        ___qtablewidgetitem2->setText(QCoreApplication::translate("MainWindow", "\345\257\206\347\240\201", nullptr));
        QTableWidgetItem *___qtablewidgetitem3 = accountTable->horizontalHeaderItem(3);
        ___qtablewidgetitem3->setText(QCoreApplication::translate("MainWindow", "\344\273\243\347\220\206", nullptr));
        QTableWidgetItem *___qtablewidgetitem4 = accountTable->horizontalHeaderItem(4);
        ___qtablewidgetitem4->setText(QCoreApplication::translate("MainWindow", "Token", nullptr));
        QTableWidgetItem *___qtablewidgetitem5 = accountTable->horizontalHeaderItem(5);
        ___qtablewidgetitem5->setText(QCoreApplication::translate("MainWindow", "\347\212\266\346\200\201", nullptr));
        usernameEdit->setPlaceholderText(QCoreApplication::translate("MainWindow", "\350\264\246\345\217\267", nullptr));
        passwordEdit->setPlaceholderText(QCoreApplication::translate("MainWindow", "\345\257\206\347\240\201", nullptr));
        gameCombo->setItemText(0, QCoreApplication::translate("MainWindow", "\347\216\213\350\200\205\350\215\243\350\200\200", nullptr));
        gameCombo->setItemText(1, QCoreApplication::translate("MainWindow", "\345\222\214\345\271\263\347\262\276\350\213\261", nullptr));
        gameCombo->setItemText(2, QCoreApplication::translate("MainWindow", "\347\201\253\345\275\261\345\277\215\350\200\205", nullptr));
        gameCombo->setItemText(3, QCoreApplication::translate("MainWindow", "\350\213\261\351\233\204\350\201\224\347\233\237\346\211\213\346\270\270", nullptr));

        label_5->setText(QCoreApplication::translate("MainWindow", "\346\270\270\346\210\217:", nullptr));
        refreshButton->setText(QCoreApplication::translate("MainWindow", "\345\274\200\345\247\213\345\210\267\346\226\260", nullptr));
        stopButton->setText(QCoreApplication::translate("MainWindow", "\345\201\234\346\255\242", nullptr));
        loginButton->setText(QCoreApplication::translate("MainWindow", "\347\231\273\345\275\225", nullptr));
        payPasswordEdit->setPlaceholderText(QCoreApplication::translate("MainWindow", "\346\224\257\344\273\230\345\257\206\347\240\201", nullptr));
        QTableWidgetItem *___qtablewidgetitem6 = advancedTable->horizontalHeaderItem(0);
        ___qtablewidgetitem6->setText(QCoreApplication::translate("MainWindow", "\345\205\263\351\224\256\350\257\215", nullptr));
        QTableWidgetItem *___qtablewidgetitem7 = advancedTable->horizontalHeaderItem(1);
        ___qtablewidgetitem7->setText(QCoreApplication::translate("MainWindow", "\346\234\200\344\275\216\344\273\267", nullptr));
        QTableWidgetItem *___qtablewidgetitem8 = groupStatsTable->horizontalHeaderItem(0);
        ___qtablewidgetitem8->setText(QCoreApplication::translate("MainWindow", "\345\210\206\347\273\204\345\220\215\347\247\260", nullptr));
        QTableWidgetItem *___qtablewidgetitem9 = groupStatsTable->horizontalHeaderItem(1);
        ___qtablewidgetitem9->setText(QCoreApplication::translate("MainWindow", "\345\205\263\351\224\256\350\257\215\346\225\260\351\207\217", nullptr));
        priceFilterCheck->setText(QCoreApplication::translate("MainWindow", "\351\231\220\344\273\267", nullptr));
        focusOnlyCheck->setText(QCoreApplication::translate("MainWindow", "\345\217\252\345\210\267\346\226\260\345\205\263\346\263\250\345\217\221\345\215\225\344\272\272", nullptr));
        intervalCheck->setText(QCoreApplication::translate("MainWindow", "\345\220\257\347\224\250\351\227\264\351\232\224", nullptr));
        priceMinEdit->setPlaceholderText(QCoreApplication::translate("MainWindow", "\346\234\200\344\275\216\344\273\267", nullptr));
        filterKeywordEdit->setPlaceholderText(QCoreApplication::translate("MainWindow", "\345\244\232\344\270\252\345\205\263\351\224\256\350\257\215\347\224\250\347\251\272\346\240\274\345\210\206\351\232\224", nullptr));
        priceMaxEdit->setPlaceholderText(QCoreApplication::translate("MainWindow", "\346\234\200\351\253\230\344\273\267", nullptr));
        batchLoginButton->setText(QCoreApplication::translate("MainWindow", "\346\211\271\351\207\217\347\231\273\345\275\225", nullptr));
        advancedModeCheck->setText(QCoreApplication::translate("MainWindow", "\345\220\257\347\224\250\351\253\230\347\272\247\347\255\233\351\200\211", nullptr));
        excludeKeywordEdit->setPlaceholderText(QCoreApplication::translate("MainWindow", "\346\216\222\351\231\244\345\205\263\351\224\256\350\257\215\357\274\214\347\224\250\347\251\272\346\240\274\345\210\206\351\232\224", nullptr));
        QTableWidgetItem *___qtablewidgetitem10 = orderTable->horizontalHeaderItem(0);
        ___qtablewidgetitem10->setText(QCoreApplication::translate("MainWindow", "\345\272\217\345\217\267", nullptr));
        QTableWidgetItem *___qtablewidgetitem11 = orderTable->horizontalHeaderItem(1);
        ___qtablewidgetitem11->setText(QCoreApplication::translate("MainWindow", "\350\256\242\345\215\225ID", nullptr));
        QTableWidgetItem *___qtablewidgetitem12 = orderTable->horizontalHeaderItem(2);
        ___qtablewidgetitem12->setText(QCoreApplication::translate("MainWindow", "\346\240\207\351\242\230", nullptr));
        QTableWidgetItem *___qtablewidgetitem13 = orderTable->horizontalHeaderItem(3);
        ___qtablewidgetitem13->setText(QCoreApplication::translate("MainWindow", "\344\273\267\346\240\274", nullptr));
        QTableWidgetItem *___qtablewidgetitem14 = orderTable->horizontalHeaderItem(4);
        ___qtablewidgetitem14->setText(QCoreApplication::translate("MainWindow", "\345\217\221\345\270\203\350\200\205", nullptr));
        QTableWidgetItem *___qtablewidgetitem15 = orderTable->horizontalHeaderItem(5);
        ___qtablewidgetitem15->setText(QCoreApplication::translate("MainWindow", "\345\214\272\346\234\215", nullptr));
        QTableWidgetItem *___qtablewidgetitem16 = orderTable->horizontalHeaderItem(6);
        ___qtablewidgetitem16->setText(QCoreApplication::translate("MainWindow", "\346\227\266\351\227\264", nullptr));
        pageSizeLabel->setText(QCoreApplication::translate("MainWindow", "\346\234\200\345\244\247\345\215\225\346\225\260:", nullptr));
        QTableWidgetItem *___qtablewidgetitem17 = resultTable->horizontalHeaderItem(0);
        ___qtablewidgetitem17->setText(QCoreApplication::translate("MainWindow", "\345\272\217\345\217\267", nullptr));
        QTableWidgetItem *___qtablewidgetitem18 = resultTable->horizontalHeaderItem(1);
        ___qtablewidgetitem18->setText(QCoreApplication::translate("MainWindow", "\350\256\242\345\215\225ID", nullptr));
        QTableWidgetItem *___qtablewidgetitem19 = resultTable->horizontalHeaderItem(2);
        ___qtablewidgetitem19->setText(QCoreApplication::translate("MainWindow", "\346\240\207\351\242\230", nullptr));
        QTableWidgetItem *___qtablewidgetitem20 = resultTable->horizontalHeaderItem(3);
        ___qtablewidgetitem20->setText(QCoreApplication::translate("MainWindow", "\344\273\267\346\240\274", nullptr));
        QTableWidgetItem *___qtablewidgetitem21 = resultTable->horizontalHeaderItem(4);
        ___qtablewidgetitem21->setText(QCoreApplication::translate("MainWindow", "\345\217\221\345\270\203\350\200\205", nullptr));
        QTableWidgetItem *___qtablewidgetitem22 = resultTable->horizontalHeaderItem(5);
        ___qtablewidgetitem22->setText(QCoreApplication::translate("MainWindow", "\345\214\272\346\234\215", nullptr));
        QTableWidgetItem *___qtablewidgetitem23 = resultTable->horizontalHeaderItem(6);
        ___qtablewidgetitem23->setText(QCoreApplication::translate("MainWindow", "\346\227\266\351\227\264", nullptr));
        stopTargetLabel->setText(QCoreApplication::translate("MainWindow", "\345\201\234\346\212\242\346\225\260\351\207\217:", nullptr));
        stopAfterTargetCheck->setText(QString());
    } // retranslateUi

};

namespace Ui {
    class MainWindow: public Ui_MainWindow {};
} // namespace Ui

QT_END_NAMESPACE

#endif // UI_MAINWINDOW_H
