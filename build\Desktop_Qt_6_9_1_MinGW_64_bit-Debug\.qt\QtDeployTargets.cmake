set(__QT_DEPLOY_TARGET_zlib_FILE C:/eee/cc/build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/zlib/libzlib.dll)
set(__QT_DEPLOY_TARGET_zlib_TYPE SHARED_LIBRARY)
set(__QT_DEPLOY_TARGET_zlib_RUNTIME_DLLS )
set(__QT_DEPLOY_TARGET_zlibstatic_FILE C:/eee/cc/build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/zlib/libzlibstatic.a)
set(__QT_DEPLOY_TARGET_zlibstatic_TYPE STATIC_LIBRARY)
set(__QT_DEPLOY_TARGET_example_FILE C:/eee/cc/build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/zlib/example.exe)
set(__QT_DEPLOY_TARGET_example_TYPE EXECUTABLE)
set(__QT_DEPLOY_TARGET_example_RUNTIME_DLLS C:/eee/cc/build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/zlib/libzlib.dll)
set(__QT_DEPLOY_TARGET_minigzip_FILE C:/eee/cc/build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/zlib/minigzip.exe)
set(__QT_DEPLOY_TARGET_minigzip_TYPE EXECUTABLE)
set(__QT_DEPLOY_TARGET_minigzip_RUNTIME_DLLS C:/eee/cc/build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/zlib/libzlib.dll)
set(__QT_DEPLOY_TARGET_example64_FILE C:/eee/cc/build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/zlib/example64.exe)
set(__QT_DEPLOY_TARGET_example64_TYPE EXECUTABLE)
set(__QT_DEPLOY_TARGET_example64_RUNTIME_DLLS C:/eee/cc/build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/zlib/libzlib.dll)
set(__QT_DEPLOY_TARGET_minigzip64_FILE C:/eee/cc/build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/zlib/minigzip64.exe)
set(__QT_DEPLOY_TARGET_minigzip64_TYPE EXECUTABLE)
set(__QT_DEPLOY_TARGET_minigzip64_RUNTIME_DLLS C:/eee/cc/build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/zlib/libzlib.dll)
set(__QT_DEPLOY_TARGET_OrderManager_FILE C:/eee/cc/build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/OrderManager.exe)
set(__QT_DEPLOY_TARGET_OrderManager_TYPE EXECUTABLE)
set(__QT_DEPLOY_TARGET_OrderManager_RUNTIME_DLLS C:/Qt/6.9.1/mingw_64/bin/Qt6Widgets.dll;C:/Qt/6.9.1/mingw_64/bin/Qt6Network.dll;C:/Qt/6.9.1/mingw_64/bin/Qt6Concurrent.dll;C:/Qt/6.9.1/mingw_64/bin/Qt6Gui.dll;C:/Qt/6.9.1/mingw_64/bin/Qt6Core.dll)
