# CMake generated Testfile for 
# Source directory: C:/Libraries/zlib131/zlib-1.3.1
# Build directory: C:/eee/cc/build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/zlib
# 
# This file includes the relevant testing commands required for 
# testing this directory and lists subdirectories to be tested as well.
add_test(example "example")
set_tests_properties(example PROPERTIES  _BACKTRACE_TRIPLES "C:/Libraries/zlib131/zlib-1.3.1/CMakeLists.txt;203;add_test;C:/Libraries/zlib131/zlib-1.3.1/CMakeLists.txt;0;")
add_test(example64 "example64")
set_tests_properties(example64 PROPERTIES  _BACKTRACE_TRIPLES "C:/Libraries/zlib131/zlib-1.3.1/CMakeLists.txt;212;add_test;C:/Libraries/zlib131/zlib-1.3.1/CMakeLists.txt;0;")
