#include "modern_integration.h"
#include "../../mainwindow.h"
#include "../core/utils/logger.h"
#include "../config/app_config.h"

ModernIntegration::ModernIntegration(MainWindow* mainWindow, QObject* parent)
    : QObject(parent), m_mainWindow(mainWindow)
{
    NEW_LOG_INFO(NewLogCategory::INTEGRATION, "ModernIntegration created with smart pointer management");
}

// 析构函数不需要实现，智能指针自动清理

bool ModernIntegration::initialize()
{
    if (m_initialized) {
        NEW_LOG_WARNING(NewLogCategory::INTEGRATION, "ModernIntegration already initialized");
        return true;
    }

    if (!m_mainWindow) {
        NEW_LOG_ERROR(NewLogCategory::INTEGRATION, "MainWindow is null, cannot initialize");
        return false;
    }

    try {
        // 使用make_unique创建智能指针管理的对象
        setupNetworkManager();
        setupLegacyApiAdapter();

        m_initialized = true;
        NEW_LOG_INFO(NewLogCategory::INTEGRATION, "ModernIntegration initialized successfully");
        return true;
    }
    catch (const std::exception& e) {
        NEW_LOG_ERROR(NewLogCategory::INTEGRATION, 
                     QString("Failed to initialize ModernIntegration: %1").arg(e.what()));
        return false;
    }
}

void ModernIntegration::shutdown()
{
    if (!m_initialized) {
        NEW_LOG_INFO(NewLogCategory::INTEGRATION, "ModernIntegration already shutdown");
        return;
    }

    // 智能指针自动清理，按相反顺序重置
    m_legacyApiAdapter.reset();
    m_tlsAdapter.reset();
    m_orderService.reset();
    m_appController.reset();
    m_networkManager.reset();

    m_initialized = false;
    NEW_LOG_INFO(NewLogCategory::INTEGRATION, "ModernIntegration shutdown completed");
}

bool ModernIntegration::startOrderRefresh()
{
    if (!m_initialized || !m_appController) {
        NEW_LOG_ERROR(NewLogCategory::ORDER, "Cannot start refresh: not initialized");
        return false;
    }

    m_appController->startOrderRefresh();
    return true;
}

void ModernIntegration::stopOrderRefresh()
{
    if (m_appController) {
        m_appController->stopOrderRefresh();
    }
}

bool ModernIntegration::loginMainAccount(const QString& username, const QString& password)
{
    if (!m_initialized || !m_appController) {
        NEW_LOG_ERROR(NewLogCategory::LOGIN, "Cannot login: not initialized");
        return false;
    }

    return m_appController->loginMainAccount(username, password);
}

void ModernIntegration::logoutAllAccounts()
{
    if (m_appController) {
        m_appController->logoutAllAccounts();
    }
}

void ModernIntegration::setupNetworkManager()
{
    m_networkManager = std::make_unique<NetworkManager>(this);
    NEW_LOG_INFO(NewLogCategory::INTEGRATION, "NetworkManager created");
}

void ModernIntegration::setupLegacyApiAdapter()
{
    m_legacyApiAdapter = std::make_unique<LegacyApiAdapter>(this);
    NEW_LOG_INFO(NewLogCategory::INTEGRATION, "LegacyApiAdapter created");
}

void ModernIntegration::setupAppController()
{
    // 创建应用控制器
    m_appController = new AppController(this);

    // 设置主窗口
    m_appController->setMainWindow(m_mainWindow);

    // 初始化控制器
    if (!m_appController->initialize()) {
        throw std::runtime_error("Failed to initialize AppController");
    }

    NEW_LOG_INFO(NewLogCategory::SYSTEM, "AppController setup completed");
}

void ModernIntegration::connectSignals()
{
    // 连接AppController信号
    connect(m_appController, &AppController::initialized,
            this, &ModernIntegration::onAppControllerInitialized);
    connect(m_appController, &AppController::errorOccurred,
            this, &ModernIntegration::onAppControllerError);
    connect(m_appController, &AppController::ordersRefreshed,
            this, &ModernIntegration::onOrdersRefreshed);
    connect(m_appController, &AppController::mainAccountLoggedIn,
            this, &ModernIntegration::onMainAccountLoggedIn);
    connect(m_appController, &AppController::subAccountLoggedIn,
            this, &ModernIntegration::onSubAccountLoggedIn);

    // 连接OrderService信号
    if (m_orderService) {
        connect(m_orderService, &OrderService::ordersUpdated,
                this, &ModernIntegration::ordersUpdated);
        connect(m_orderService, &OrderService::refreshStarted,
                this, [this]() { emit refreshStatusChanged(true); });
        connect(m_orderService, &OrderService::refreshCompleted,
                this, [this](bool, const QString&) { emit refreshStatusChanged(false); });
    }

    NEW_LOG_INFO(NewLogCategory::SYSTEM, "Signal connections established");
}

void ModernIntegration::onAppControllerInitialized()
{
    NEW_LOG_INFO(NewLogCategory::SYSTEM, "AppController initialized");
}

void ModernIntegration::onAppControllerError(const QString& error)
{
    NEW_LOG_ERROR(NewLogCategory::SYSTEM, QString("AppController error: %1").arg(error));
    emit integrationError(error);
}

void ModernIntegration::onOrdersRefreshed(const QList<OrderInfo>& orders)
{
    NEW_LOG_INFO(NewLogCategory::ORDER, QString("Orders refreshed: %1 orders").arg(orders.size()));
    emit ordersUpdated(orders);
}

void ModernIntegration::onMainAccountLoggedIn(const MainAccountInfo& account)
{
    NEW_LOG_INFO(NewLogCategory::LOGIN, QString("Main account logged in: %1").arg(account.username));
    emit accountLoggedIn(account.username);
}

void ModernIntegration::onSubAccountLoggedIn(const AccountInfo& account)
{
    NEW_LOG_INFO(NewLogCategory::LOGIN, QString("Sub account logged in: %1").arg(account.username));
    emit accountLoggedIn(account.username);
}
