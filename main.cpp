#include <QApplication>
#include <QStyleFactory>
#include <QDebug>
#include <QLoggingCategory>
#include <exception>
#include "src/ui/mainwindow.h"

// Qt消息处理器
void messageOutput(QtMsgType type, const QMessageLogContext &context, const QString &msg)
{
    QString txt;
    switch (type) {
    case QtDebugMsg:
        txt = QString("Debug: %1").arg(msg);
        break;
    case QtWarningMsg:
        txt = QString("Warning: %1").arg(msg);
        break;
    case QtCriticalMsg:
        txt = QString("Critical: %1").arg(msg);
        break;
    case QtFatalMsg:
        txt = QString("Fatal: %1").arg(msg);
        break;
    case QtInfoMsg:
        txt = QString("Info: %1").arg(msg);
        break;
    }

    // 输出到控制台
    fprintf(stderr, "%s\n", txt.toLocal8Bit().constData());
    fflush(stderr);
}

int main(int argc, char *argv[])
{
    try {
        // 安装消息处理器
        qInstallMessageHandler(messageOutput);

        qDebug() << "🔍 [DEBUG] 应用程序启动";

        QApplication app(argc, argv);

        // 设置应用程序信息
        app.setApplicationName("订单管理器");
        app.setApplicationVersion("1.0.0");
        app.setOrganizationName("OrderManager");

        // 设置应用程序样式
        app.setStyle(QStyleFactory::create("Fusion"));

        qDebug() << "🔍 [DEBUG] 创建主窗口";
        MainWindow window;
        window.show();

        qDebug() << "🔍 [DEBUG] 进入事件循环";
        return app.exec();

    } catch (const std::exception& e) {
        qDebug() << "🔍 [DEBUG] 主程序捕获异常:" << e.what();
        return -1;
    } catch (...) {
        qDebug() << "🔍 [DEBUG] 主程序捕获未知异常";
        return -1;
    }
}