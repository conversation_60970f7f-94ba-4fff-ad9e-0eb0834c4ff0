
---
events:
  -
    kind: "message-v1"
    backtrace:
      - "C:/Qt/Tools/CMake_64/share/cmake-3.30/Modules/CMakeDetermineSystem.cmake:205 (message)"
      - "CMakeLists.txt:3 (project)"
    message: |
      The system is: Windows - 10.0.26100 - AMD64
...

---
events:
  -
    kind: "message-v1"
    backtrace:
      - "C:/Qt/Tools/CMake_64/share/cmake-3.30/Modules/CMakeDetermineCompilerId.cmake:17 (message)"
      - "C:/Qt/Tools/CMake_64/share/cmake-3.30/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "C:/Qt/Tools/CMake_64/share/cmake-3.30/Modules/CMakeDetermineCXXCompiler.cmake:126 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:3 (project)"
    message: |
      Compiling the CXX compiler identification source file "CMakeCXXCompilerId.cpp" failed.
      Compiler: C:/Qt/Tools/mingw1310_64/bin/g++.exe 
      Build flags: 
      Id flags:  
      
      The output was:
      1
      
      
  -
    kind: "message-v1"
    backtrace:
      - "C:/Qt/Tools/CMake_64/share/cmake-3.30/Modules/CMakeDetermineCompilerId.cmake:17 (message)"
      - "C:/Qt/Tools/CMake_64/share/cmake-3.30/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "C:/Qt/Tools/CMake_64/share/cmake-3.30/Modules/CMakeDetermineCXXCompiler.cmake:126 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:3 (project)"
    message: |
      Compiling the CXX compiler identification source file "CMakeCXXCompilerId.cpp" failed.
      Compiler: C:/Qt/Tools/mingw1310_64/bin/g++.exe 
      Build flags: 
      Id flags: -c 
      
      The output was:
      1
      
      
  -
    kind: "message-v1"
    backtrace:
      - "C:/Qt/Tools/CMake_64/share/cmake-3.30/Modules/CMakeDetermineCompilerId.cmake:17 (message)"
      - "C:/Qt/Tools/CMake_64/share/cmake-3.30/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "C:/Qt/Tools/CMake_64/share/cmake-3.30/Modules/CMakeDetermineCXXCompiler.cmake:126 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:3 (project)"
    message: |
      Compiling the CXX compiler identification source file "CMakeCXXCompilerId.cpp" failed.
      Compiler: C:/Qt/Tools/mingw1310_64/bin/g++.exe 
      Build flags: 
      Id flags: --c++ 
      
      The output was:
      1
      g++.exe: error: unrecognized command-line option '--c++'
      
      
  -
    kind: "message-v1"
    backtrace:
      - "C:/Qt/Tools/CMake_64/share/cmake-3.30/Modules/CMakeDetermineCompilerId.cmake:17 (message)"
      - "C:/Qt/Tools/CMake_64/share/cmake-3.30/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "C:/Qt/Tools/CMake_64/share/cmake-3.30/Modules/CMakeDetermineCXXCompiler.cmake:126 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:3 (project)"
    message: |
      Compiling the CXX compiler identification source file "CMakeCXXCompilerId.cpp" failed.
      Compiler: C:/Qt/Tools/mingw1310_64/bin/g++.exe 
      Build flags: 
      Id flags: --ec++ 
      
      The output was:
      1
      g++.exe: error: unrecognized command-line option '--ec++'; did you mean '-Weffc++'?
      
      
  -
    kind: "message-v1"
    backtrace:
      - "C:/Qt/Tools/CMake_64/share/cmake-3.30/Modules/CMakeDetermineCompilerId.cmake:17 (message)"
      - "C:/Qt/Tools/CMake_64/share/cmake-3.30/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "C:/Qt/Tools/CMake_64/share/cmake-3.30/Modules/CMakeDetermineCXXCompiler.cmake:126 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:3 (project)"
    message: |
      Compiling the CXX compiler identification source file "CMakeCXXCompilerId.cpp" failed.
      Compiler: C:/Qt/Tools/mingw1310_64/bin/g++.exe 
      Build flags: 
      Id flags: --target=arm-arm-none-eabi;-mcpu=cortex-m3 
      
      The output was:
      1
      g++.exe: warning: '-mcpu=' is deprecated; use '-mtune=' or '-march=' instead
      g++.exe: error: unrecognized command-line option '--target=arm-arm-none-eabi'
      
      
  -
    kind: "message-v1"
    backtrace:
      - "C:/Qt/Tools/CMake_64/share/cmake-3.30/Modules/CMakeDetermineCompilerId.cmake:17 (message)"
      - "C:/Qt/Tools/CMake_64/share/cmake-3.30/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "C:/Qt/Tools/CMake_64/share/cmake-3.30/Modules/CMakeDetermineCXXCompiler.cmake:126 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:3 (project)"
    message: |
      Compiling the CXX compiler identification source file "CMakeCXXCompilerId.cpp" failed.
      Compiler: C:/Qt/Tools/mingw1310_64/bin/g++.exe 
      Build flags: 
      Id flags: -c;-I__does_not_exist__ 
      
      The output was:
      1
      
      
  -
    kind: "message-v1"
    backtrace:
      - "C:/Qt/Tools/CMake_64/share/cmake-3.30/Modules/CMakeDetermineCompilerId.cmake:17 (message)"
      - "C:/Qt/Tools/CMake_64/share/cmake-3.30/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "C:/Qt/Tools/CMake_64/share/cmake-3.30/Modules/CMakeDetermineCXXCompiler.cmake:126 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:3 (project)"
    message: |
      Compiling the CXX compiler identification source file "CMakeCXXCompilerId.cpp" failed.
      Compiler: C:/Qt/Tools/mingw1310_64/bin/g++.exe 
      Build flags: 
      Id flags:  
      
      The output was:
      1
      
      
  -
    kind: "message-v1"
    backtrace:
      - "C:/Qt/Tools/CMake_64/share/cmake-3.30/Modules/CMakeDetermineCompilerId.cmake:17 (message)"
      - "C:/Qt/Tools/CMake_64/share/cmake-3.30/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "C:/Qt/Tools/CMake_64/share/cmake-3.30/Modules/CMakeDetermineCXXCompiler.cmake:126 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:3 (project)"
    message: |
      Compiling the CXX compiler identification source file "CMakeCXXCompilerId.cpp" failed.
      Compiler: C:/Qt/Tools/mingw1310_64/bin/g++.exe 
      Build flags: 
      Id flags: -c 
      
      The output was:
      1
      
      
  -
    kind: "message-v1"
    backtrace:
      - "C:/Qt/Tools/CMake_64/share/cmake-3.30/Modules/CMakeDetermineCompilerId.cmake:17 (message)"
      - "C:/Qt/Tools/CMake_64/share/cmake-3.30/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "C:/Qt/Tools/CMake_64/share/cmake-3.30/Modules/CMakeDetermineCXXCompiler.cmake:126 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:3 (project)"
    message: |
      Compiling the CXX compiler identification source file "CMakeCXXCompilerId.cpp" failed.
      Compiler: C:/Qt/Tools/mingw1310_64/bin/g++.exe 
      Build flags: 
      Id flags: --c++ 
      
      The output was:
      1
      g++.exe: error: unrecognized command-line option '--c++'
      
      
  -
    kind: "message-v1"
    backtrace:
      - "C:/Qt/Tools/CMake_64/share/cmake-3.30/Modules/CMakeDetermineCompilerId.cmake:17 (message)"
      - "C:/Qt/Tools/CMake_64/share/cmake-3.30/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "C:/Qt/Tools/CMake_64/share/cmake-3.30/Modules/CMakeDetermineCXXCompiler.cmake:126 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:3 (project)"
    message: |
      Compiling the CXX compiler identification source file "CMakeCXXCompilerId.cpp" failed.
      Compiler: C:/Qt/Tools/mingw1310_64/bin/g++.exe 
      Build flags: 
      Id flags: --ec++ 
      
      The output was:
      1
      g++.exe: error: unrecognized command-line option '--ec++'; did you mean '-Weffc++'?
      
      
  -
    kind: "message-v1"
    backtrace:
      - "C:/Qt/Tools/CMake_64/share/cmake-3.30/Modules/CMakeDetermineCompilerId.cmake:17 (message)"
      - "C:/Qt/Tools/CMake_64/share/cmake-3.30/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "C:/Qt/Tools/CMake_64/share/cmake-3.30/Modules/CMakeDetermineCXXCompiler.cmake:126 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:3 (project)"
    message: |
      Compiling the CXX compiler identification source file "CMakeCXXCompilerId.cpp" failed.
      Compiler: C:/Qt/Tools/mingw1310_64/bin/g++.exe 
      Build flags: 
      Id flags: --target=arm-arm-none-eabi;-mcpu=cortex-m3 
      
      The output was:
      1
      g++.exe: warning: '-mcpu=' is deprecated; use '-mtune=' or '-march=' instead
      g++.exe: error: unrecognized command-line option '--target=arm-arm-none-eabi'
      
      
  -
    kind: "message-v1"
    backtrace:
      - "C:/Qt/Tools/CMake_64/share/cmake-3.30/Modules/CMakeDetermineCompilerId.cmake:17 (message)"
      - "C:/Qt/Tools/CMake_64/share/cmake-3.30/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "C:/Qt/Tools/CMake_64/share/cmake-3.30/Modules/CMakeDetermineCXXCompiler.cmake:126 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:3 (project)"
    message: |
      Compiling the CXX compiler identification source file "CMakeCXXCompilerId.cpp" failed.
      Compiler: C:/Qt/Tools/mingw1310_64/bin/g++.exe 
      Build flags: 
      Id flags: -c;-I__does_not_exist__ 
      
      The output was:
      1
      
      
  -
    kind: "message-v1"
    backtrace:
      - "C:/Qt/Tools/CMake_64/share/cmake-3.30/Modules/CMakeDetermineCompilerId.cmake:1205 (message)"
      - "C:/Qt/Tools/CMake_64/share/cmake-3.30/Modules/CMakeDetermineCompilerId.cmake:86 (CMAKE_DETERMINE_COMPILER_ID_VENDOR)"
      - "C:/Qt/Tools/CMake_64/share/cmake-3.30/Modules/CMakeDetermineCXXCompiler.cmake:126 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:3 (project)"
    message: |
      Checking whether the CXX compiler is IAR using "" did not match "IAR .+ Compiler":
      g++.exe: fatal error: no input files
      compilation terminated.
  -
    kind: "message-v1"
    backtrace:
      - "C:/Qt/Tools/CMake_64/share/cmake-3.30/Modules/CMakeDetermineCompilerId.cmake:1205 (message)"
      - "C:/Qt/Tools/CMake_64/share/cmake-3.30/Modules/CMakeDetermineCompilerId.cmake:86 (CMAKE_DETERMINE_COMPILER_ID_VENDOR)"
      - "C:/Qt/Tools/CMake_64/share/cmake-3.30/Modules/CMakeDetermineCXXCompiler.cmake:126 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:3 (project)"
    message: |
      Checking whether the CXX compiler is IAR using "" did not match "IAR .+ Compiler":
      g++.exe: fatal error: no input files
      compilation terminated.
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Qt/Tools/CMake_64/share/cmake-3.30/Modules/CMakeDetermineCompilerABI.cmake:74 (try_compile)"
      - "C:/Qt/Tools/CMake_64/share/cmake-3.30/Modules/CMakeTestCXXCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:3 (project)"
    checks:
      - "Detecting CXX compiler ABI info"
    directories:
      source: "C:/eee/cc/build/Fresh-Debug/CMakeFiles/CMakeScratch/TryCompile-2ef60m"
      binary: "C:/eee/cc/build/Fresh-Debug/CMakeFiles/CMakeScratch/TryCompile-2ef60m"
    cmakeVariables:
      CMAKE_CXX_FLAGS: ""
      CMAKE_CXX_FLAGS_DEBUG: ""
      CMAKE_CXX_SCAN_FOR_MODULES: "OFF"
      CMAKE_EXE_LINKER_FLAGS: ""
    buildResult:
      variable: "CMAKE_CXX_ABI_COMPILED"
      cached: true
      stdout: |
        Change Dir: 'C:/eee/cc/build/Fresh-Debug/CMakeFiles/CMakeScratch/TryCompile-2ef60m'
        
        Run Build Command(s): C:/Qt/Tools/CMake_64/bin/cmake.exe -E env VERBOSE=1 C:\\Qt\\Tools\\mingw1310_64\\bin\\mingw32-make.exe -f Makefile cmTC_748a6/fast
        C:/Qt/Tools/mingw1310_64/bin/mingw32-make  -f CMakeFiles\\cmTC_748a6.dir\\build.make CMakeFiles/cmTC_748a6.dir/build
        mingw32-make[1]: Entering directory 'C:/eee/cc/build/Fresh-Debug/CMakeFiles/CMakeScratch/TryCompile-2ef60m'
        Building CXX object CMakeFiles/cmTC_748a6.dir/CMakeCXXCompilerABI.cpp.obj
        C:\\Qt\\Tools\\mingw1310_64\\bin\\g++.exe    -o CMakeFiles\\cmTC_748a6.dir\\CMakeCXXCompilerABI.cpp.obj -c C:\\Qt\\Tools\\CMake_64\\share\\cmake-3.30\\Modules\\CMakeCXXCompilerABI.cpp
        mingw32-make[1]: *** [CMakeFiles\\cmTC_748a6.dir\\build.make:77: CMakeFiles/cmTC_748a6.dir/CMakeCXXCompilerABI.cpp.obj] Error 1
        mingw32-make[1]: Leaving directory 'C:/eee/cc/build/Fresh-Debug/CMakeFiles/CMakeScratch/TryCompile-2ef60m'
        mingw32-make: *** [Makefile:126: cmTC_748a6/fast] Error 2
        
      exitCode: 2
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Qt/Tools/CMake_64/share/cmake-3.30/Modules/CMakeTestCXXCompiler.cmake:56 (try_compile)"
      - "CMakeLists.txt:3 (project)"
    checks:
      - "Check for working CXX compiler: C:/Qt/Tools/mingw1310_64/bin/g++.exe"
    directories:
      source: "C:/eee/cc/build/Fresh-Debug/CMakeFiles/CMakeScratch/TryCompile-3voz67"
      binary: "C:/eee/cc/build/Fresh-Debug/CMakeFiles/CMakeScratch/TryCompile-3voz67"
    cmakeVariables:
      CMAKE_CXX_FLAGS: ""
      CMAKE_CXX_FLAGS_DEBUG: ""
      CMAKE_CXX_SCAN_FOR_MODULES: "OFF"
      CMAKE_EXE_LINKER_FLAGS: ""
    buildResult:
      variable: "CMAKE_CXX_COMPILER_WORKS"
      cached: true
      stdout: |
        Change Dir: 'C:/eee/cc/build/Fresh-Debug/CMakeFiles/CMakeScratch/TryCompile-3voz67'
        
        Run Build Command(s): C:/Qt/Tools/CMake_64/bin/cmake.exe -E env VERBOSE=1 C:\\Qt\\Tools\\mingw1310_64\\bin\\mingw32-make.exe -f Makefile cmTC_cac4e/fast
        C:/Qt/Tools/mingw1310_64/bin/mingw32-make  -f CMakeFiles\\cmTC_cac4e.dir\\build.make CMakeFiles/cmTC_cac4e.dir/build
        mingw32-make[1]: Entering directory 'C:/eee/cc/build/Fresh-Debug/CMakeFiles/CMakeScratch/TryCompile-3voz67'
        Building CXX object CMakeFiles/cmTC_cac4e.dir/testCXXCompiler.cxx.obj
        C:\\Qt\\Tools\\mingw1310_64\\bin\\g++.exe    -o CMakeFiles\\cmTC_cac4e.dir\\testCXXCompiler.cxx.obj -c C:\\eee\\cc\\build\\Fresh-Debug\\CMakeFiles\\CMakeScratch\\TryCompile-3voz67\\testCXXCompiler.cxx
        mingw32-make[1]: *** [CMakeFiles\\cmTC_cac4e.dir\\build.make:77: CMakeFiles/cmTC_cac4e.dir/testCXXCompiler.cxx.obj] Error 1
        mingw32-make[1]: Leaving directory 'C:/eee/cc/build/Fresh-Debug/CMakeFiles/CMakeScratch/TryCompile-3voz67'
        mingw32-make: *** [Makefile:126: cmTC_cac4e/fast] Error 2
        
      exitCode: 2
...
