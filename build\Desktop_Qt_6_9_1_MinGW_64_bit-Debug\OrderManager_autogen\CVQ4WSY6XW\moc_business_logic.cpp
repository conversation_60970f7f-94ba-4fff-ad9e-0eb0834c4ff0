/****************************************************************************
** Meta object code from reading C++ file 'business_logic.h'
**
** Created by: The Qt Meta Object Compiler version 69 (Qt 6.9.1)
**
** WARNING! All changes made in this file will be lost!
*****************************************************************************/

#include "../../../../legacy/services/business_logic.h"
#include <QtCore/qmetatype.h>
#include <QtCore/QList>

#include <QtCore/qtmochelpers.h>

#include <memory>


#include <QtCore/qxptype_traits.h>
#if !defined(Q_MOC_OUTPUT_REVISION)
#error "The header file 'business_logic.h' doesn't include <QObject>."
#elif Q_MOC_OUTPUT_REVISION != 69
#error "This file was generated using the moc from 6.9.1. It"
#error "cannot be used with the include files from this version of Qt."
#error "(The moc has changed too much.)"
#endif

#ifndef Q_CONSTINIT
#define Q_CONSTINIT
#endif

QT_WARNING_PUSH
QT_WARNING_DISABLE_DEPRECATED
QT_WARNING_DISABLE_GCC("-Wuseless-cast")
namespace {
struct qt_meta_tag_ZN18LoginBusinessLogicE_t {};
} // unnamed namespace

template <> constexpr inline auto LoginBusinessLogic::qt_create_metaobjectdata<qt_meta_tag_ZN18LoginBusinessLogicE_t>()
{
    namespace QMC = QtMocConstants;
    QtMocHelpers::StringRefStorage qt_stringData {
        "LoginBusinessLogic",
        "loginStarted",
        "",
        "username",
        "loginProgress",
        "step",
        "percentage",
        "loginCompleted",
        "success",
        "message",
        "loginFailed",
        "error",
        "retryCount",
        "handleRetryTimer"
    };

    QtMocHelpers::UintData qt_methods {
        // Signal 'loginStarted'
        QtMocHelpers::SignalData<void(const QString &)>(1, 2, QMC::AccessPublic, QMetaType::Void, {{
            { QMetaType::QString, 3 },
        }}),
        // Signal 'loginProgress'
        QtMocHelpers::SignalData<void(const QString &, int)>(4, 2, QMC::AccessPublic, QMetaType::Void, {{
            { QMetaType::QString, 5 }, { QMetaType::Int, 6 },
        }}),
        // Signal 'loginCompleted'
        QtMocHelpers::SignalData<void(bool, const QString &)>(7, 2, QMC::AccessPublic, QMetaType::Void, {{
            { QMetaType::Bool, 8 }, { QMetaType::QString, 9 },
        }}),
        // Signal 'loginFailed'
        QtMocHelpers::SignalData<void(const QString &, int)>(10, 2, QMC::AccessPublic, QMetaType::Void, {{
            { QMetaType::QString, 11 }, { QMetaType::Int, 12 },
        }}),
        // Slot 'handleRetryTimer'
        QtMocHelpers::SlotData<void()>(13, 2, QMC::AccessPrivate, QMetaType::Void),
    };
    QtMocHelpers::UintData qt_properties {
    };
    QtMocHelpers::UintData qt_enums {
    };
    return QtMocHelpers::metaObjectData<LoginBusinessLogic, qt_meta_tag_ZN18LoginBusinessLogicE_t>(QMC::MetaObjectFlag{}, qt_stringData,
            qt_methods, qt_properties, qt_enums);
}
Q_CONSTINIT const QMetaObject LoginBusinessLogic::staticMetaObject = { {
    QMetaObject::SuperData::link<QObject::staticMetaObject>(),
    qt_staticMetaObjectStaticContent<qt_meta_tag_ZN18LoginBusinessLogicE_t>.stringdata,
    qt_staticMetaObjectStaticContent<qt_meta_tag_ZN18LoginBusinessLogicE_t>.data,
    qt_static_metacall,
    nullptr,
    qt_staticMetaObjectRelocatingContent<qt_meta_tag_ZN18LoginBusinessLogicE_t>.metaTypes,
    nullptr
} };

void LoginBusinessLogic::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    auto *_t = static_cast<LoginBusinessLogic *>(_o);
    if (_c == QMetaObject::InvokeMetaMethod) {
        switch (_id) {
        case 0: _t->loginStarted((*reinterpret_cast< std::add_pointer_t<QString>>(_a[1]))); break;
        case 1: _t->loginProgress((*reinterpret_cast< std::add_pointer_t<QString>>(_a[1])),(*reinterpret_cast< std::add_pointer_t<int>>(_a[2]))); break;
        case 2: _t->loginCompleted((*reinterpret_cast< std::add_pointer_t<bool>>(_a[1])),(*reinterpret_cast< std::add_pointer_t<QString>>(_a[2]))); break;
        case 3: _t->loginFailed((*reinterpret_cast< std::add_pointer_t<QString>>(_a[1])),(*reinterpret_cast< std::add_pointer_t<int>>(_a[2]))); break;
        case 4: _t->handleRetryTimer(); break;
        default: ;
        }
    }
    if (_c == QMetaObject::IndexOfMethod) {
        if (QtMocHelpers::indexOfMethod<void (LoginBusinessLogic::*)(const QString & )>(_a, &LoginBusinessLogic::loginStarted, 0))
            return;
        if (QtMocHelpers::indexOfMethod<void (LoginBusinessLogic::*)(const QString & , int )>(_a, &LoginBusinessLogic::loginProgress, 1))
            return;
        if (QtMocHelpers::indexOfMethod<void (LoginBusinessLogic::*)(bool , const QString & )>(_a, &LoginBusinessLogic::loginCompleted, 2))
            return;
        if (QtMocHelpers::indexOfMethod<void (LoginBusinessLogic::*)(const QString & , int )>(_a, &LoginBusinessLogic::loginFailed, 3))
            return;
    }
}

const QMetaObject *LoginBusinessLogic::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *LoginBusinessLogic::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_staticMetaObjectStaticContent<qt_meta_tag_ZN18LoginBusinessLogicE_t>.strings))
        return static_cast<void*>(this);
    return QObject::qt_metacast(_clname);
}

int LoginBusinessLogic::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = QObject::qt_metacall(_c, _id, _a);
    if (_id < 0)
        return _id;
    if (_c == QMetaObject::InvokeMetaMethod) {
        if (_id < 5)
            qt_static_metacall(this, _c, _id, _a);
        _id -= 5;
    }
    if (_c == QMetaObject::RegisterMethodArgumentMetaType) {
        if (_id < 5)
            *reinterpret_cast<QMetaType *>(_a[0]) = QMetaType();
        _id -= 5;
    }
    return _id;
}

// SIGNAL 0
void LoginBusinessLogic::loginStarted(const QString & _t1)
{
    QMetaObject::activate<void>(this, &staticMetaObject, 0, nullptr, _t1);
}

// SIGNAL 1
void LoginBusinessLogic::loginProgress(const QString & _t1, int _t2)
{
    QMetaObject::activate<void>(this, &staticMetaObject, 1, nullptr, _t1, _t2);
}

// SIGNAL 2
void LoginBusinessLogic::loginCompleted(bool _t1, const QString & _t2)
{
    QMetaObject::activate<void>(this, &staticMetaObject, 2, nullptr, _t1, _t2);
}

// SIGNAL 3
void LoginBusinessLogic::loginFailed(const QString & _t1, int _t2)
{
    QMetaObject::activate<void>(this, &staticMetaObject, 3, nullptr, _t1, _t2);
}
namespace {
struct qt_meta_tag_ZN18OrderBusinessLogicE_t {};
} // unnamed namespace

template <> constexpr inline auto OrderBusinessLogic::qt_create_metaobjectdata<qt_meta_tag_ZN18OrderBusinessLogicE_t>()
{
    namespace QMC = QtMocConstants;
    QtMocHelpers::StringRefStorage qt_stringData {
        "OrderBusinessLogic",
        "ordersRefreshStarted",
        "",
        "ordersRefreshed",
        "QList<ResponseProcessor::OrderInfo>",
        "orders",
        "orderAcceptStarted",
        "orderId",
        "orderAccepted",
        "success",
        "message",
        "orderFilterChanged",
        "totalCount",
        "filteredCount",
        "handleAutoRefreshTimer"
    };

    QtMocHelpers::UintData qt_methods {
        // Signal 'ordersRefreshStarted'
        QtMocHelpers::SignalData<void()>(1, 2, QMC::AccessPublic, QMetaType::Void),
        // Signal 'ordersRefreshed'
        QtMocHelpers::SignalData<void(const QList<ResponseProcessor::OrderInfo> &)>(3, 2, QMC::AccessPublic, QMetaType::Void, {{
            { 0x80000000 | 4, 5 },
        }}),
        // Signal 'orderAcceptStarted'
        QtMocHelpers::SignalData<void(const QString &)>(6, 2, QMC::AccessPublic, QMetaType::Void, {{
            { QMetaType::QString, 7 },
        }}),
        // Signal 'orderAccepted'
        QtMocHelpers::SignalData<void(const QString &, bool, const QString &)>(8, 2, QMC::AccessPublic, QMetaType::Void, {{
            { QMetaType::QString, 7 }, { QMetaType::Bool, 9 }, { QMetaType::QString, 10 },
        }}),
        // Signal 'orderFilterChanged'
        QtMocHelpers::SignalData<void(int, int)>(11, 2, QMC::AccessPublic, QMetaType::Void, {{
            { QMetaType::Int, 12 }, { QMetaType::Int, 13 },
        }}),
        // Slot 'handleAutoRefreshTimer'
        QtMocHelpers::SlotData<void()>(14, 2, QMC::AccessPrivate, QMetaType::Void),
    };
    QtMocHelpers::UintData qt_properties {
    };
    QtMocHelpers::UintData qt_enums {
    };
    return QtMocHelpers::metaObjectData<OrderBusinessLogic, qt_meta_tag_ZN18OrderBusinessLogicE_t>(QMC::MetaObjectFlag{}, qt_stringData,
            qt_methods, qt_properties, qt_enums);
}
Q_CONSTINIT const QMetaObject OrderBusinessLogic::staticMetaObject = { {
    QMetaObject::SuperData::link<QObject::staticMetaObject>(),
    qt_staticMetaObjectStaticContent<qt_meta_tag_ZN18OrderBusinessLogicE_t>.stringdata,
    qt_staticMetaObjectStaticContent<qt_meta_tag_ZN18OrderBusinessLogicE_t>.data,
    qt_static_metacall,
    nullptr,
    qt_staticMetaObjectRelocatingContent<qt_meta_tag_ZN18OrderBusinessLogicE_t>.metaTypes,
    nullptr
} };

void OrderBusinessLogic::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    auto *_t = static_cast<OrderBusinessLogic *>(_o);
    if (_c == QMetaObject::InvokeMetaMethod) {
        switch (_id) {
        case 0: _t->ordersRefreshStarted(); break;
        case 1: _t->ordersRefreshed((*reinterpret_cast< std::add_pointer_t<QList<ResponseProcessor::OrderInfo>>>(_a[1]))); break;
        case 2: _t->orderAcceptStarted((*reinterpret_cast< std::add_pointer_t<QString>>(_a[1]))); break;
        case 3: _t->orderAccepted((*reinterpret_cast< std::add_pointer_t<QString>>(_a[1])),(*reinterpret_cast< std::add_pointer_t<bool>>(_a[2])),(*reinterpret_cast< std::add_pointer_t<QString>>(_a[3]))); break;
        case 4: _t->orderFilterChanged((*reinterpret_cast< std::add_pointer_t<int>>(_a[1])),(*reinterpret_cast< std::add_pointer_t<int>>(_a[2]))); break;
        case 5: _t->handleAutoRefreshTimer(); break;
        default: ;
        }
    }
    if (_c == QMetaObject::IndexOfMethod) {
        if (QtMocHelpers::indexOfMethod<void (OrderBusinessLogic::*)()>(_a, &OrderBusinessLogic::ordersRefreshStarted, 0))
            return;
        if (QtMocHelpers::indexOfMethod<void (OrderBusinessLogic::*)(const QList<ResponseProcessor::OrderInfo> & )>(_a, &OrderBusinessLogic::ordersRefreshed, 1))
            return;
        if (QtMocHelpers::indexOfMethod<void (OrderBusinessLogic::*)(const QString & )>(_a, &OrderBusinessLogic::orderAcceptStarted, 2))
            return;
        if (QtMocHelpers::indexOfMethod<void (OrderBusinessLogic::*)(const QString & , bool , const QString & )>(_a, &OrderBusinessLogic::orderAccepted, 3))
            return;
        if (QtMocHelpers::indexOfMethod<void (OrderBusinessLogic::*)(int , int )>(_a, &OrderBusinessLogic::orderFilterChanged, 4))
            return;
    }
}

const QMetaObject *OrderBusinessLogic::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *OrderBusinessLogic::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_staticMetaObjectStaticContent<qt_meta_tag_ZN18OrderBusinessLogicE_t>.strings))
        return static_cast<void*>(this);
    return QObject::qt_metacast(_clname);
}

int OrderBusinessLogic::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = QObject::qt_metacall(_c, _id, _a);
    if (_id < 0)
        return _id;
    if (_c == QMetaObject::InvokeMetaMethod) {
        if (_id < 6)
            qt_static_metacall(this, _c, _id, _a);
        _id -= 6;
    }
    if (_c == QMetaObject::RegisterMethodArgumentMetaType) {
        if (_id < 6)
            *reinterpret_cast<QMetaType *>(_a[0]) = QMetaType();
        _id -= 6;
    }
    return _id;
}

// SIGNAL 0
void OrderBusinessLogic::ordersRefreshStarted()
{
    QMetaObject::activate(this, &staticMetaObject, 0, nullptr);
}

// SIGNAL 1
void OrderBusinessLogic::ordersRefreshed(const QList<ResponseProcessor::OrderInfo> & _t1)
{
    QMetaObject::activate<void>(this, &staticMetaObject, 1, nullptr, _t1);
}

// SIGNAL 2
void OrderBusinessLogic::orderAcceptStarted(const QString & _t1)
{
    QMetaObject::activate<void>(this, &staticMetaObject, 2, nullptr, _t1);
}

// SIGNAL 3
void OrderBusinessLogic::orderAccepted(const QString & _t1, bool _t2, const QString & _t3)
{
    QMetaObject::activate<void>(this, &staticMetaObject, 3, nullptr, _t1, _t2, _t3);
}

// SIGNAL 4
void OrderBusinessLogic::orderFilterChanged(int _t1, int _t2)
{
    QMetaObject::activate<void>(this, &staticMetaObject, 4, nullptr, _t1, _t2);
}
namespace {
struct qt_meta_tag_ZN17UserBusinessLogicE_t {};
} // unnamed namespace

template <> constexpr inline auto UserBusinessLogic::qt_create_metaobjectdata<qt_meta_tag_ZN17UserBusinessLogicE_t>()
{
    namespace QMC = QtMocConstants;
    QtMocHelpers::StringRefStorage qt_stringData {
        "UserBusinessLogic",
        "userInfoUpdated",
        "",
        "ResponseProcessor::UserInfo",
        "userInfo",
        "balanceUpdated",
        "balance",
        "payPasswordStatusChanged",
        "hasPayPassword"
    };

    QtMocHelpers::UintData qt_methods {
        // Signal 'userInfoUpdated'
        QtMocHelpers::SignalData<void(const ResponseProcessor::UserInfo &)>(1, 2, QMC::AccessPublic, QMetaType::Void, {{
            { 0x80000000 | 3, 4 },
        }}),
        // Signal 'balanceUpdated'
        QtMocHelpers::SignalData<void(double)>(5, 2, QMC::AccessPublic, QMetaType::Void, {{
            { QMetaType::Double, 6 },
        }}),
        // Signal 'payPasswordStatusChanged'
        QtMocHelpers::SignalData<void(bool)>(7, 2, QMC::AccessPublic, QMetaType::Void, {{
            { QMetaType::Bool, 8 },
        }}),
    };
    QtMocHelpers::UintData qt_properties {
    };
    QtMocHelpers::UintData qt_enums {
    };
    return QtMocHelpers::metaObjectData<UserBusinessLogic, qt_meta_tag_ZN17UserBusinessLogicE_t>(QMC::MetaObjectFlag{}, qt_stringData,
            qt_methods, qt_properties, qt_enums);
}
Q_CONSTINIT const QMetaObject UserBusinessLogic::staticMetaObject = { {
    QMetaObject::SuperData::link<QObject::staticMetaObject>(),
    qt_staticMetaObjectStaticContent<qt_meta_tag_ZN17UserBusinessLogicE_t>.stringdata,
    qt_staticMetaObjectStaticContent<qt_meta_tag_ZN17UserBusinessLogicE_t>.data,
    qt_static_metacall,
    nullptr,
    qt_staticMetaObjectRelocatingContent<qt_meta_tag_ZN17UserBusinessLogicE_t>.metaTypes,
    nullptr
} };

void UserBusinessLogic::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    auto *_t = static_cast<UserBusinessLogic *>(_o);
    if (_c == QMetaObject::InvokeMetaMethod) {
        switch (_id) {
        case 0: _t->userInfoUpdated((*reinterpret_cast< std::add_pointer_t<ResponseProcessor::UserInfo>>(_a[1]))); break;
        case 1: _t->balanceUpdated((*reinterpret_cast< std::add_pointer_t<double>>(_a[1]))); break;
        case 2: _t->payPasswordStatusChanged((*reinterpret_cast< std::add_pointer_t<bool>>(_a[1]))); break;
        default: ;
        }
    }
    if (_c == QMetaObject::IndexOfMethod) {
        if (QtMocHelpers::indexOfMethod<void (UserBusinessLogic::*)(const ResponseProcessor::UserInfo & )>(_a, &UserBusinessLogic::userInfoUpdated, 0))
            return;
        if (QtMocHelpers::indexOfMethod<void (UserBusinessLogic::*)(double )>(_a, &UserBusinessLogic::balanceUpdated, 1))
            return;
        if (QtMocHelpers::indexOfMethod<void (UserBusinessLogic::*)(bool )>(_a, &UserBusinessLogic::payPasswordStatusChanged, 2))
            return;
    }
}

const QMetaObject *UserBusinessLogic::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *UserBusinessLogic::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_staticMetaObjectStaticContent<qt_meta_tag_ZN17UserBusinessLogicE_t>.strings))
        return static_cast<void*>(this);
    return QObject::qt_metacast(_clname);
}

int UserBusinessLogic::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = QObject::qt_metacall(_c, _id, _a);
    if (_id < 0)
        return _id;
    if (_c == QMetaObject::InvokeMetaMethod) {
        if (_id < 3)
            qt_static_metacall(this, _c, _id, _a);
        _id -= 3;
    }
    if (_c == QMetaObject::RegisterMethodArgumentMetaType) {
        if (_id < 3)
            *reinterpret_cast<QMetaType *>(_a[0]) = QMetaType();
        _id -= 3;
    }
    return _id;
}

// SIGNAL 0
void UserBusinessLogic::userInfoUpdated(const ResponseProcessor::UserInfo & _t1)
{
    QMetaObject::activate<void>(this, &staticMetaObject, 0, nullptr, _t1);
}

// SIGNAL 1
void UserBusinessLogic::balanceUpdated(double _t1)
{
    QMetaObject::activate<void>(this, &staticMetaObject, 1, nullptr, _t1);
}

// SIGNAL 2
void UserBusinessLogic::payPasswordStatusChanged(bool _t1)
{
    QMetaObject::activate<void>(this, &staticMetaObject, 2, nullptr, _t1);
}
QT_WARNING_POP
