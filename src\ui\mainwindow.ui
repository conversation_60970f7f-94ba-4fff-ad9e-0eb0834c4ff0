<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>MainWindow</class>
 <widget class="QMainWindow" name="MainWindow">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>1200</width>
    <height>700</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>订单管理器</string>
  </property>
  <widget class="QWidget" name="centralwidget">
   <widget class="QTextEdit" name="logText">
    <property name="geometry">
     <rect>
      <x>10</x>
      <y>390</y>
      <width>421</width>
      <height>261</height>
     </rect>
    </property>
   </widget>
   <widget class="QTableWidget" name="accountTable">
    <property name="geometry">
     <rect>
      <x>440</x>
      <y>10</y>
      <width>581</width>
      <height>131</height>
     </rect>
    </property>
    <property name="columnCount">
     <number>6</number>
    </property>
    <attribute name="horizontalHeaderStretchLastSection">
     <bool>true</bool>
    </attribute>
    <column>
     <property name="text">
      <string>序号</string>
     </property>
    </column>
    <column>
     <property name="text">
      <string>用户名</string>
     </property>
    </column>
    <column>
     <property name="text">
      <string>密码</string>
     </property>
    </column>
    <column>
     <property name="text">
      <string>代理</string>
     </property>
    </column>
    <column>
     <property name="text">
      <string>Token</string>
     </property>
    </column>
    <column>
     <property name="text">
      <string>状态</string>
     </property>
    </column>
   </widget>
   <widget class="QLineEdit" name="usernameEdit">
    <property name="geometry">
     <rect>
      <x>10</x>
      <y>10</y>
      <width>167</width>
      <height>25</height>
     </rect>
    </property>
    <property name="placeholderText">
     <string>账号</string>
    </property>
   </widget>
   <widget class="QLineEdit" name="passwordEdit">
    <property name="geometry">
     <rect>
      <x>10</x>
      <y>40</y>
      <width>167</width>
      <height>25</height>
     </rect>
    </property>
    <property name="echoMode">
     <enum>QLineEdit::EchoMode::Normal</enum>
    </property>
    <property name="placeholderText">
     <string>密码</string>
    </property>
   </widget>
   <widget class="QComboBox" name="gameCombo">
    <property name="geometry">
     <rect>
      <x>50</x>
      <y>100</y>
      <width>121</width>
      <height>25</height>
     </rect>
    </property>
    <item>
     <property name="text">
      <string>王者荣耀</string>
     </property>
    </item>
    <item>
     <property name="text">
      <string>和平精英</string>
     </property>
    </item>
    <item>
     <property name="text">
      <string>火影忍者</string>
     </property>
    </item>
    <item>
     <property name="text">
      <string>英雄联盟手游</string>
     </property>
    </item>
   </widget>
   <widget class="QLabel" name="label_5">
    <property name="geometry">
     <rect>
      <x>10</x>
      <y>100</y>
      <width>34</width>
      <height>19</height>
     </rect>
    </property>
    <property name="text">
     <string>游戏:</string>
    </property>
   </widget>
   <widget class="QPushButton" name="refreshButton">
    <property name="geometry">
     <rect>
      <x>190</x>
      <y>100</y>
      <width>93</width>
      <height>28</height>
     </rect>
    </property>
    <property name="minimumSize">
     <size>
      <width>93</width>
      <height>0</height>
     </size>
    </property>
    <property name="text">
     <string>开始刷新</string>
    </property>
   </widget>
   <widget class="QPushButton" name="stopButton">
    <property name="geometry">
     <rect>
      <x>290</x>
      <y>100</y>
      <width>93</width>
      <height>28</height>
     </rect>
    </property>
    <property name="minimumSize">
     <size>
      <width>93</width>
      <height>0</height>
     </size>
    </property>
    <property name="text">
     <string>停止</string>
    </property>
   </widget>
   <widget class="QPushButton" name="loginButton">
    <property name="geometry">
     <rect>
      <x>190</x>
      <y>40</y>
      <width>93</width>
      <height>28</height>
     </rect>
    </property>
    <property name="minimumSize">
     <size>
      <width>93</width>
      <height>0</height>
     </size>
    </property>
    <property name="text">
     <string>登录</string>
    </property>
   </widget>
   <widget class="QLineEdit" name="payPasswordEdit">
    <property name="geometry">
     <rect>
      <x>10</x>
      <y>70</y>
      <width>167</width>
      <height>25</height>
     </rect>
    </property>
    <property name="echoMode">
     <enum>QLineEdit::EchoMode::Normal</enum>
    </property>
    <property name="placeholderText">
     <string>支付密码</string>
    </property>
   </widget>
   <widget class="QTableWidget" name="advancedTable">
    <property name="geometry">
     <rect>
      <x>10</x>
      <y>230</y>
      <width>421</width>
      <height>151</height>
     </rect>
    </property>
    <column>
     <property name="text">
      <string>关键词</string>
     </property>
    </column>
    <column>
     <property name="text">
      <string>最低价</string>
     </property>
    </column>
   </widget>
   

   <widget class="QFrame" name="statsLine">
    <property name="geometry">
     <rect>
      <x>10</x>
      <y>385</y>
      <width>421</width>
      <height>3</height>
     </rect>
    </property>
    <property name="frameShape">
     <enum>QFrame::Shape::HLine</enum>
    </property>
    <property name="frameShadow">
     <enum>QFrame::Shadow::Sunken</enum>
    </property>
   </widget>
   <widget class="QTableWidget" name="groupStatsTable">
    <property name="geometry">
     <rect>
      <x>1030</x>
      <y>10</y>
      <width>161</width>
      <height>131</height>
     </rect>
    </property>
    <property name="editTriggers">
     <set>QAbstractItemView::EditTrigger::NoEditTriggers</set>
    </property>
    <property name="alternatingRowColors">
     <bool>true</bool>
    </property>
    <property name="selectionMode">
     <enum>QAbstractItemView::SelectionMode::SingleSelection</enum>
    </property>
    <property name="textElideMode">
     <enum>Qt::TextElideMode::ElideMiddle</enum>
    </property>
    <attribute name="horizontalHeaderMinimumSectionSize">
     <number>80</number>
    </attribute>
    <attribute name="horizontalHeaderDefaultSectionSize">
     <number>100</number>
    </attribute>
    <attribute name="horizontalHeaderStretchLastSection">
     <bool>false</bool>
    </attribute>
    <column>
     <property name="text">
      <string>分组名称</string>
     </property>
    </column>
    <column>
     <property name="text">
      <string>关键词数量</string>
     </property>
    </column>
   </widget>
   <widget class="QCheckBox" name="priceFilterCheck">
    <property name="geometry">
     <rect>
      <x>190</x>
      <y>10</y>
      <width>117</width>
      <height>23</height>
     </rect>
    </property>
    <property name="text">
     <string>限价</string>
    </property>
   </widget>
   <widget class="QCheckBox" name="focusOnlyCheck">
    <property name="geometry">
     <rect>
      <x>290</x>
      <y>70</y>
      <width>147</width>
      <height>23</height>
     </rect>
    </property>
    <property name="text">
     <string>只刷新关注发单人</string>
    </property>
   </widget>
   <widget class="QCheckBox" name="intervalCheck">
    <property name="geometry">
     <rect>
      <x>280</x>
      <y>200</y>
      <width>91</width>
      <height>23</height>
     </rect>
    </property>
    <property name="text">
     <string>启用间隔</string>
    </property>
    <property name="checked">
     <bool>true</bool>
    </property>
   </widget>
   <widget class="QSpinBox" name="pageSizeSpin">
    <property name="geometry">
     <rect>
      <x>360</x>
      <y>40</y>
      <width>49</width>
      <height>25</height>
     </rect>
    </property>
    <property name="minimum">
     <number>1</number>
    </property>
    <property name="maximum">
     <number>200</number>
    </property>
    <property name="value">
     <number>20</number>
    </property>
   </widget>
   <widget class="QLineEdit" name="priceMinEdit">
    <property name="geometry">
     <rect>
      <x>250</x>
      <y>10</y>
      <width>61</width>
      <height>25</height>
     </rect>
    </property>
    <property name="placeholderText">
     <string>最低价</string>
    </property>
   </widget>
   <widget class="QLineEdit" name="filterKeywordEdit">
    <property name="geometry">
     <rect>
      <x>10</x>
      <y>138</y>
      <width>421</width>
      <height>25</height>
     </rect>
    </property>
    <property name="placeholderText">
     <string>多个关键词用空格分隔</string>
    </property>
   </widget>
   <widget class="QLineEdit" name="priceMaxEdit">
    <property name="geometry">
     <rect>
      <x>320</x>
      <y>10</y>
      <width>61</width>
      <height>25</height>
     </rect>
    </property>
    <property name="placeholderText">
     <string>最高价</string>
    </property>
   </widget>
   <widget class="QPushButton" name="batchLoginButton">
    <property name="geometry">
     <rect>
      <x>190</x>
      <y>70</y>
      <width>93</width>
      <height>28</height>
     </rect>
    </property>
    <property name="minimumSize">
     <size>
      <width>93</width>
      <height>0</height>
     </size>
    </property>
    <property name="text">
     <string>批量登录</string>
    </property>
   </widget>
   <widget class="QCheckBox" name="advancedModeCheck">
    <property name="geometry">
     <rect>
      <x>10</x>
      <y>200</y>
      <width>117</width>
      <height>23</height>
     </rect>
    </property>
    <property name="text">
     <string>启用高级筛选</string>
    </property>
   </widget>
   <widget class="QLineEdit" name="excludeKeywordEdit">
    <property name="geometry">
     <rect>
      <x>10</x>
      <y>170</y>
      <width>421</width>
      <height>25</height>
     </rect>
    </property>
    <property name="placeholderText">
     <string>排除关键词，用空格分隔</string>
    </property>
   </widget>
   <widget class="QTableWidget" name="orderTable">
    <property name="geometry">
     <rect>
      <x>440</x>
      <y>340</y>
      <width>751</width>
      <height>311</height>
     </rect>
    </property>
    <property name="columnCount">
     <number>7</number>
    </property>
    <attribute name="horizontalHeaderStretchLastSection">
     <bool>true</bool>
    </attribute>
    <column>
     <property name="text">
      <string>序号</string>
     </property>
    </column>
    <column>
     <property name="text">
      <string>订单ID</string>
     </property>
    </column>
    <column>
     <property name="text">
      <string>标题</string>
     </property>
    </column>
    <column>
     <property name="text">
      <string>价格</string>
     </property>
    </column>
    <column>
     <property name="text">
      <string>发布者</string>
     </property>
    </column>
    <column>
     <property name="text">
      <string>区服</string>
     </property>
    </column>
    <column>
     <property name="text">
      <string>时间</string>
     </property>
    </column>
   </widget>
   <widget class="QLabel" name="pageSizeLabel">
    <property name="geometry">
     <rect>
      <x>290</x>
      <y>40</y>
      <width>71</width>
      <height>19</height>
     </rect>
    </property>
    <property name="text">
     <string>最大单数:</string>
    </property>
   </widget>
   <widget class="QTableWidget" name="resultTable">
    <property name="geometry">
     <rect>
      <x>440</x>
      <y>150</y>
      <width>751</width>
      <height>181</height>
     </rect>
    </property>
    <property name="columnCount">
     <number>7</number>
    </property>
    <attribute name="horizontalHeaderStretchLastSection">
     <bool>true</bool>
    </attribute>
    <column>
     <property name="text">
      <string>序号</string>
     </property>
    </column>
    <column>
     <property name="text">
      <string>订单ID</string>
     </property>
    </column>
    <column>
     <property name="text">
      <string>标题</string>
     </property>
    </column>
    <column>
     <property name="text">
      <string>价格</string>
     </property>
    </column>
    <column>
     <property name="text">
      <string>发布者</string>
     </property>
    </column>
    <column>
     <property name="text">
      <string>区服</string>
     </property>
    </column>
    <column>
     <property name="text">
      <string>时间</string>
     </property>
    </column>
   </widget>
   <widget class="QSpinBox" name="intervalSpin">
    <property name="geometry">
     <rect>
      <x>360</x>
      <y>200</y>
      <width>61</width>
      <height>25</height>
     </rect>
    </property>
    <property name="minimum">
     <number>0</number>
    </property>
    <property name="maximum">
     <number>60000</number>
    </property>
    <property name="value">
     <number>3000</number>
    </property>
   </widget>
   <widget class="QLabel" name="mainAcctIntervalLabel">
    <property name="geometry">
     <rect>
      <x>10</x>
      <y>230</y>
      <width>120</width>
      <height>23</height>
     </rect>
    </property>
    <property name="text">
     <string>主账号刷新间隔(ms):</string>
    </property>
   </widget>
   <widget class="QSpinBox" name="mainAcctIntervalSpin">
    <property name="geometry">
     <rect>
      <x>140</x>
      <y>230</y>
      <width>61</width>
      <height>25</height>
     </rect>
    </property>
    <property name="minimum">
     <number>1000</number>
    </property>
    <property name="maximum">
     <number>60000</number>
    </property>
    <property name="value">
     <number>5000</number>
    </property>
   </widget>
   <widget class="QCheckBox" name="highSpeedModeCheck">
    <property name="geometry">
     <rect>
      <x>220</x>
      <y>230</y>
      <width>120</width>
      <height>23</height>
     </rect>
    </property>
    <property name="text">
     <string>高速刷新模式</string>
    </property>
    <property name="toolTip">
     <string>启用后批量刷新时主账号将以最高速度刷新（约100ms间隔）</string>
    </property>
   </widget>
   <widget class="QLabel" name="stopTargetLabel">
    <property name="geometry">
     <rect>
      <x>150</x>
      <y>200</y>
      <width>71</width>
      <height>19</height>
     </rect>
    </property>
    <property name="text">
     <string>停抢数量:</string>
    </property>
   </widget>
   <widget class="QSpinBox" name="acceptTargetSpin">
    <property name="geometry">
     <rect>
      <x>210</x>
      <y>200</y>
      <width>41</width>
      <height>25</height>
     </rect>
    </property>
    <property name="minimum">
     <number>1</number>
    </property>
    <property name="maximum">
     <number>1000</number>
    </property>
    <property name="value">
     <number>5</number>
    </property>
   </widget>
   <widget class="QCheckBox" name="stopAfterTargetCheck">
    <property name="geometry">
     <rect>
      <x>130</x>
      <y>200</y>
      <width>21</width>
      <height>25</height>
     </rect>
    </property>
    <property name="text">
     <string/>
    </property>
    <property name="checked">
     <bool>true</bool>
    </property>
   </widget>
  </widget>
  <widget class="QMenuBar" name="menubar">
   <property name="geometry">
    <rect>
     <x>0</x>
     <y>0</y>
     <width>1200</width>
     <height>25</height>
    </rect>
   </property>
  </widget>
  <widget class="QStatusBar" name="statusbar"/>
 </widget>
 <resources/>
 <connections/>
</ui>
